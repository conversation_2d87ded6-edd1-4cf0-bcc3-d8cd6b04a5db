import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

// GET semua customer
export async function GET(request: Request) {
  try {
    // Ambil URL parameters
    const { searchParams } = new URL(request.url);
    const search = searchParams.get('search');
    const limit = searchParams.get('limit');
    const page = searchParams.get('page') || '1';
    const startDate = searchParams.get('startDate');
    const endDate = searchParams.get('endDate');
    const includeTransactionHistory = searchParams.get('includeTransactionHistory') === 'true';

    console.log(`API Customers - Request parameters: search=${search}, startDate=${startDate}, endDate=${endDate}, page=${page}, limit=${limit}, includeTransactionHistory=${includeTransactionHistory}`);

    // Build query
    const where: Record<string, any> = {};

    // Filter berdasarkan pencarian (name, email, atau phone)
    if (search) {
      where.OR = [
        { name: { contains: search, mode: 'insensitive' } },
        { email: { contains: search, mode: 'insensitive' } },
        { phone: { contains: search } }
      ];
    }

    // Ambil parameter jenis filter
    const filterType = searchParams.get('filterType') || 'registration';
    console.log(`API Customers - Filter type: ${filterType}`);

    // Filter berdasarkan tanggal
    if (startDate || endDate) {
      if (filterType === 'registration') {
        // Filter berdasarkan tanggal registrasi
        where.registeredAt = {};

        if (startDate) {
          // Konversi ke tanggal awal hari (00:00:00)
          const startDateTime = new Date(startDate);
          startDateTime.setHours(0, 0, 0, 0);
          where.registeredAt.gte = startDateTime;
          console.log(`API Customers - Filtering by registration startDate: ${startDateTime.toISOString()}`);
        }

        if (endDate) {
          // Konversi ke tanggal akhir hari (23:59:59)
          const endDateTime = new Date(endDate);
          endDateTime.setHours(23, 59, 59, 999);
          where.registeredAt.lte = endDateTime;
          console.log(`API Customers - Filtering by registration endDate: ${endDateTime.toISOString()}`);
        }
      } else if (filterType === 'transaction') {
        // Filter berdasarkan tanggal transaksi
        // Catatan: Ini memerlukan pendekatan berbeda karena kita perlu filter berdasarkan relasi
        console.log(`API Customers - Using transaction date filter, will be applied post-query`);
      }
    }

    // Hitung total customer untuk pagination
    const totalCustomers = await prisma.customer.count({ where });

    // Set pagination
    const pageSize = limit ? parseInt(limit) : 100; // Meningkatkan batas default menjadi 100
    const pageNumber = parseInt(page);
    const skip = (pageNumber - 1) * pageSize;

    // Ambil customer dari database
    let customers = await prisma.customer.findMany({
      where: {
        isActive: true, // Hanya ambil pelanggan aktif
        ...where // Tambahkan filter pencarian jika ada
      },
      select: { // Pilih field yang relevan untuk tabel
        id: true,
        name: true,
        phone: true,
        points: true,
        tags: true,
        address: true, // <-- Tambahkan address
        registeredAt: true, // <-- Pilih registeredAt
        // createdAt: false, // <-- Jangan pilih createdAt jika tidak perlu

        // Jika filter berdasarkan tanggal transaksi atau includeTransactionHistory=true, kita perlu mengambil data transaksi juga
        ...((filterType === 'transaction' && (startDate || endDate)) || includeTransactionHistory ? {
          transactions: {
            select: {
              id: true,
              createdAt: true,
              totalAmount: true,
              paymentMethod: true,
              displayId: true,
              therapist: {
                select: {
                  id: true,
                  name: true
                }
              },
              outlet: {
                select: {
                  id: true,
                  name: true
                }
              },
              transactionItems: {
                select: {
                  id: true,
                  price: true,
                  quantity: true,
                  service: {
                    select: {
                      id: true,
                      name: true
                    }
                  }
                }
              },
              createdBy: {
                select: {
                  id: true,
                  name: true
                }
              }
            },
            orderBy: {
              createdAt: 'desc'
            }
          }
        } : {})
      },
      orderBy: search ? [
        // Jika ada pencarian, prioritaskan hasil yang paling relevan
        // Urutkan berdasarkan kecocokan nama terlebih dahulu
        { name: 'asc' },
        { phone: 'asc' },
        { registeredAt: 'desc' }
      ] : {
        registeredAt: 'desc' // Default: urutkan berdasarkan tanggal registrasi
      },
      skip,
      take: pageSize
    });

    // Jika filter berdasarkan tanggal transaksi, kita perlu memfilter data setelah diambil
    let filteredTotalCustomers = totalCustomers;

    if ((startDate || endDate) && filterType === 'transaction') {
      console.log('Applying transaction date filter post-query...');

      // Konversi tanggal untuk perbandingan dengan penanganan zona waktu yang lebih baik
      let startDateTime = null;
      let endDateTime = null;

      if (startDate) {
        // Pastikan tanggal dimulai dari awal hari (00:00:00.000)
        startDateTime = new Date(startDate);
        startDateTime.setHours(0, 0, 0, 0);
        console.log(`Original startDate: ${startDate}, Converted to: ${startDateTime.toISOString()}`);
      }

      if (endDate) {
        // Pastikan tanggal berakhir di akhir hari (23:59:59.999)
        endDateTime = new Date(endDate);
        endDateTime.setHours(23, 59, 59, 999);
        console.log(`Original endDate: ${endDate}, Converted to: ${endDateTime.toISOString()}`);
      }

      console.log(`Filtering transactions from: ${startDateTime?.toISOString() || 'any date'}`);
      console.log(`Filtering transactions until: ${endDateTime?.toISOString() || 'any date'}`);

      // Untuk mendapatkan total yang akurat, kita perlu mengambil semua pelanggan dan memfilternya
      // Ini tidak efisien untuk database besar, tapi untuk sementara ini cukup
      // Buat where clause untuk transaksi jika ada filter tanggal
      let transactionWhere = {};

      if (startDateTime || endDateTime) {
        transactionWhere = {
          createdAt: {}
        };

        if (startDateTime) {
          transactionWhere.createdAt.gte = startDateTime;
        }

        if (endDateTime) {
          transactionWhere.createdAt.lte = endDateTime;
        }
      }

      console.log(`Transaction where clause: ${JSON.stringify(transactionWhere)}`);

      const allCustomersWithTransactions = await prisma.customer.findMany({
        where: {
          isActive: true
        },
        select: {
          id: true,
          name: true, // Tambahkan nama untuk debugging
          transactions: {
            where: Object.keys(transactionWhere).length > 0 ? transactionWhere : undefined,
            select: {
              id: true,
              createdAt: true
            }
          }
        }
      });

      console.log(`Total customers with transactions data: ${allCustomersWithTransactions.length}`);

      // Filter pelanggan yang memiliki transaksi dalam rentang tanggal
      const filteredCustomerIds = allCustomersWithTransactions
        .filter(customer => {
          // Jika tidak ada transaksi, tidak termasuk dalam filter
          if (!customer.transactions || customer.transactions.length === 0) return false;

          // Cek apakah ada transaksi dalam rentang tanggal
          return customer.transactions.some(trx => {
            // Gunakan createdAt sebagai pengganti transactionDate
            const trxDate = new Date(trx.createdAt);

            // Log untuk debugging
            if (startDate === endDate && startDate === '2023-05-01') {
              console.log(`Transaction date: ${trxDate.toISOString()}, Customer: ${customer.id}`);
            }

            // Cek apakah tanggal transaksi dalam rentang
            const afterStart = !startDateTime || trxDate >= startDateTime;
            const beforeEnd = !endDateTime || trxDate <= endDateTime;

            const isInRange = afterStart && beforeEnd;

            // Log untuk debugging tanggal 1 Mei
            if (startDate === endDate && startDate === '2023-05-01' && isInRange) {
              console.log(`MATCH FOUND! Transaction date: ${trxDate.toISOString()}, Customer: ${customer.id}`);
            }

            return isInRange;
          });
        })
        .map(customer => customer.id);

      // Update total untuk pagination
      filteredTotalCustomers = filteredCustomerIds.length;
      console.log(`Found ${filteredTotalCustomers} customers with transactions in the selected date range`);

      // Filter customers yang sudah diambil
      const filteredCustomers = customers.filter(customer => {
        // Jika tidak ada transaksi, tidak termasuk dalam filter
        if (!customer.transactions || customer.transactions.length === 0) return false;

        // Cek apakah ada transaksi dalam rentang tanggal
        return customer.transactions.some(trx => {
          const trxDate = new Date(trx.createdAt);

          // Log untuk debugging
          if (startDate === endDate && startDate === '2023-05-01') {
            console.log(`Filtered customer check - Transaction date: ${trxDate.toISOString()}, Customer: ${customer.id}`);
          }

          // Cek apakah tanggal transaksi dalam rentang
          const afterStart = !startDateTime || trxDate >= startDateTime;
          const beforeEnd = !endDateTime || trxDate <= endDateTime;

          const isInRange = afterStart && beforeEnd;

          // Log untuk debugging tanggal 1 Mei
          if (startDate === endDate && startDate === '2023-05-01' && isInRange) {
            console.log(`FILTERED CUSTOMER MATCH! Transaction date: ${trxDate.toISOString()}, Customer: ${customer.id}`);
          }

          return isInRange;
        });
      });

      console.log(`Filtered to ${filteredCustomers.length} customers for current page`);

      // Hapus data transaksi dari response untuk mengurangi ukuran data
      customers = filteredCustomers.map(customer => {
        const { transactions, ...rest } = customer as any;
        return rest;
      });
    }

    // Jika tidak ada customer, kembalikan array kosong dengan pagination
    if (customers.length === 0 && pageNumber === 1) {
      return NextResponse.json({
        message: 'Tidak ada customer yang ditemukan',
        customers: [],
        pagination: {
          total: 0,
          page: pageNumber,
          pageSize,
          pageCount: 0
        },
        filter: {
          type: filterType,
          startDate,
          endDate
        }
      });
    }

    // Kembalikan data customer dengan info pagination
    return NextResponse.json({
      message: 'Data customer berhasil diambil',
      customers,
      pagination: {
        total: filteredTotalCustomers,
        page: pageNumber,
        pageSize,
        pageCount: Math.ceil(filteredTotalCustomers / pageSize)
      },
      filter: {
        type: filterType,
        startDate,
        endDate
      }
    });
  } catch (error: unknown) {
    console.error('Error fetching customers:', error);
    return NextResponse.json(
      { error: 'Terjadi kesalahan saat mengambil data customer' },
      { status: 500 }
    );
  }
}

// POST untuk membuat customer baru
export async function POST(request: Request) {
  try {
    const body = await request.json();
    // Ambil juga points dan tags dari body jika dikirim dari frontend
    const { name, email, phone, birthdate, address, gender, points = 0, tags = [] } = body;

    // Validasi input
    if (!name || !phone) {
      return NextResponse.json(
        { error: 'Nama dan nomor telepon diperlukan' },
        { status: 400 }
      );
    }

    // Cek duplikat email jika ada
    if (email) {
      const existingEmail = await prisma.customer.findFirst({
        where: { email }
      });

      if (existingEmail) {
        return NextResponse.json(
          {
            error: 'Email sudah terdaftar',
            existingCustomer: {
              name: existingEmail.name,
              email: existingEmail.email,
              id: existingEmail.id
            }
          },
          { status: 400 }
        );
      }
    }

    // Cek duplikat nomor telepon
    const existingPhone = await prisma.customer.findFirst({
      where: { phone }
    });

    if (existingPhone) {
      return NextResponse.json(
        {
          error: 'Nomor telepon sudah terdaftar',
          existingCustomer: {
            name: existingPhone.name,
            phone: existingPhone.phone,
            id: existingPhone.id
          }
        },
        { status: 400 }
      );
    }

    // --- Logika Generate ID Kustom ---
    let nextIdNumber = 1;
    try {
      // Cari customer terakhir berdasarkan ID custom (jika ada)
      // Ini asumsi format ID lama konsisten (P + angka)
      const lastCustomer = await prisma.customer.findFirst({
        where: {
          id: { startsWith: 'P' }
        },
        orderBy: { id: 'desc' } // Urutkan berdasarkan ID string
      });

      if (lastCustomer && lastCustomer.id.startsWith('P')) {
        const lastNumber = parseInt(lastCustomer.id.substring(1));
        if (!isNaN(lastNumber)) {
          nextIdNumber = lastNumber + 1;
        }
      }
    } catch(err){
       console.error("Error fetching last customer ID for sequence:", err);
       // Jika error, tetap gunakan 1, atau implementasikan strategi lain
    }
    const newCustomerId = `P${String(nextIdNumber).padStart(7, '0')}`;
    // --- End Logika Generate ID ---

    // Proses tag pelanggan
    let customerTags = Array.isArray(tags) ? [...tags] : [];

    // Jika pelanggan tidak memiliki tag, tambahkan tag 'Baru'
    // Jika sudah memiliki tag, pertahankan tag yang ada
    if (customerTags.length === 0) {
      customerTags = ['Baru'];
    } else if (!customerTags.includes('Baru')) {
      // Jika pelanggan memiliki tag tapi tidak memiliki tag 'Baru', tambahkan tag 'Baru'
      customerTags.push('Baru');
    }

    // Buat customer baru dengan ID kustom
    const newCustomer = await prisma.customer.create({
      data: {
        id: newCustomerId, // Gunakan ID yang baru dibuat
        name,
        email: email || null,
        phone,
        birthdate: birthdate ? new Date(birthdate) : null,
        address: address || null,
        gender: gender || null,
        points: Number(points) || 0, // Pastikan points adalah number
        tags: customerTags, // Gunakan tag yang sudah diproses
        // registeredAt diisi otomatis oleh Prisma
      }
    });

    return NextResponse.json({
      message: 'Customer berhasil dibuat',
      customer: newCustomer
    }, { status: 201 });

  } catch (error: unknown) {
    // Handle potensi error jika ID duplikat (meskipun seharusnya tidak terjadi dengan logika sequence)
    if ((error as any).code === 'P2002' && (error as any).meta?.target?.includes('id')) {
      console.error('Error creating customer: Duplicate ID', error);
      return NextResponse.json(
        { error: 'Gagal membuat ID unik untuk customer. Coba lagi.' },
        { status: 500 }
      );
    }

    console.error('Error creating customer:', error);
    return NextResponse.json(
      { error: 'Terjadi kesalahan saat membuat customer' },
      { status: 500 }
    );
  }
}