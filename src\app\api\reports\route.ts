import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { Prisma } from '@prisma/client';

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const outletId = searchParams.get('outletId');

    // Ambil parameter tanggal dari query
    const rawStartDate = searchParams.get('startDate');
    const rawEndDate = searchParams.get('endDate');

    console.log(`Raw date params: startDate=${rawStartDate}, endDate=${rawEndDate}`);

    if (!rawStartDate || !rawEndDate) {
      throw new Error('Tanggal mulai dan tanggal akhir harus disediakan');
    }

    // Buat tanggal dengan pendekatan yang lebih langsung
    // Pastikan tanggal diinterpretasikan sebagai tanggal lokal, bukan UTC
    // Format: YYYY-MM-DD -> parse sebagai tanggal lokal
    const [startYear, startMonth, startDay] = rawStartDate.split('-').map(Number);
    const [endYear, endMonth, endDay] = rawEndDate.split('-').map(Number);

    // Buat tanggal dengan komponen tanggal lokal
    // Bulan dalam JavaScript dimulai dari 0 (Januari = 0)
    const startDate = new Date(startYear, startMonth - 1, startDay, 0, 0, 0, 0);
    const endDate = new Date(endYear, endMonth - 1, endDay, 23, 59, 59, 999);

    // Validasi tanggal
    if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
      console.log(`Invalid date: startDate=${startDate}, endDate=${endDate}`);
      throw new Error('Format tanggal tidak valid');
    }

    // Cek apakah tanggal mulai dan akhir adalah hari yang sama
    const isSameDay = rawStartDate === rawEndDate;
    console.log(`Is same day: ${isSameDay}`);

    // Log tanggal yang akan digunakan
    console.log(`Using dates: startDate=${startDate.toISOString()}, endDate=${endDate.toISOString()}`);
    console.log(`Local date parts: startDay=${startDate.getDate()}, endDay=${endDate.getDate()}`);

    // Jika tanggal mulai dan akhir adalah hari yang sama, pastikan rentang waktu mencakup seluruh hari
    if (isSameDay) {
      console.log(`Same day detected: ${rawStartDate}`);
    }

    // Log untuk debugging
    console.log(`Fetching reports for Outlet: ${outletId || 'All'}, Start: ${startDate.toISOString()}, End: ${endDate.toISOString()}`);

    // Filter dasar untuk transaksi berdasarkan tanggal dan outlet terpilih
    const transactionWhere: Prisma.TransactionWhereInput = {
      transactionDate: { gte: startDate, lte: endDate },
      ...(outletId && { outletId: outletId }),
      // paymentStatus: 'PAID', // Uncomment jika hanya ingin hitung yg lunas
    };

    // Log filter transaksi untuk debugging
    console.log(`Transaction filter: ${JSON.stringify({
      transactionDate: { gte: startDate.toISOString(), lte: endDate.toISOString() },
      ...(outletId && { outletId: outletId }),
    })}`);

    // Filter HANYA berdasarkan tanggal (untuk perbandingan outlet)
    const dateRangeWhere: Prisma.TransactionWhereInput = {
       transactionDate: { gte: startDate, lte: endDate },
       // paymentStatus: 'PAID', // Uncomment jika hanya ingin hitung yg lunas
    };

    // --- Mulai Agregasi ---

    // 1. Hitung Total Penjualan Keseluruhan (berdasarkan filter aktif)
    const totalRevenueData = await prisma.transaction.aggregate({
      _sum: {
        totalAmount: true,
      },
      where: transactionWhere,
    });
    const totalRevenue = totalRevenueData._sum.totalAmount || 0;

    // Log untuk debugging
    console.log(`[API GET /api/reports] Total revenue: ${totalRevenue}`);



    // 3. Laporan Layanan Teramai (Popular Services) - Gunakan filter aktif
    const popularServicesData = await prisma.transactionItem.groupBy({
      by: ['serviceId'],
      _count: { id: true },
      where: { transaction: transactionWhere },
      orderBy: { _count: { id: 'desc' } },
      take: 10,
    });

    // Ambil detail nama layanan - filter null values
    const serviceIds = popularServicesData.map(s => s.serviceId).filter(id => id !== null);
    const services = await prisma.service.findMany({
      where: { id: { in: serviceIds } },
      select: { id: true, name: true },
    });
    const serviceMap = new Map(services.map(s => [s.id, s.name]));
    const popularServicesReport = popularServicesData.map(s => ({
      serviceId: s.serviceId,
      serviceName: serviceMap.get(s.serviceId) || 'Unknown Service',
      count: s._count.id,
    }));

    // 4. Laporan Kinerja Terapis (Sales & Commission)
    // 4a. Total Penjualan per Terapis
    const therapistSales = await prisma.transaction.groupBy({
        by: ['therapistId'],
        _sum: {
            totalAmount: true,
        },
        where: transactionWhere,
    });

    // 4b. Total Komisi per Terapis (Hitung dari TransactionItem)
    const therapistCommissionsData = await prisma.transactionItem.findMany({
        where: {
            transaction: transactionWhere,
        },
        select: {
            service: { select: { commission: true } },
            transaction: { select: { therapistId: true } },
            quantity: true,
        },
    });

    const therapistCommissions = therapistCommissionsData.reduce((acc, item) => {
        const therapistId = item.transaction?.therapistId;
        const commission = (item.service?.commission ?? 0) * (item.quantity ?? 1);
        if (therapistId) {
           acc[therapistId] = (acc[therapistId] || 0) + commission;
        }
        return acc;
    }, {} as Record<string, number>);

    // Ambil detail nama terapis
    const therapistIds = Array.from(new Set([...therapistSales.map(t => t.therapistId), ...Object.keys(therapistCommissions)]));
    const therapists = await prisma.therapist.findMany({
      where: { id: { in: therapistIds } },
      select: { id: true, name: true },
    });
    const therapistMap = new Map(therapists.map(t => [t.id, t.name]));

    // Gabungkan data sales dan komisi
    const therapistPerformanceReport = therapistIds.map(id => ({
      therapistId: id,
      therapistName: therapistMap.get(id) || 'Unknown Therapist',
      totalSales: therapistSales.find(t => t.therapistId === id)?._sum.totalAmount || 0,
      totalCommission: therapistCommissions[id] || 0,
    })).sort((a, b) => (b.totalSales ?? 0) - (a.totalSales ?? 0));


    // 5. Laporan Pelanggan Tersering (Frequent Customers) - Gunakan filter aktif
    const frequentCustomersData = await prisma.transaction.groupBy({
      by: ['customerId'],
      _count: { id: true },
      _sum: { totalAmount: true },
      where: transactionWhere,
      orderBy: { _count: { id: 'desc' } },
      take: 10,
    });

    // Ambil detail nama pelanggan
    const customerIds = frequentCustomersData.map(c => c.customerId);
    const customers = await prisma.customer.findMany({
      where: { id: { in: customerIds } },
      select: { id: true, name: true, phone: true },
    });
    const customerMap = new Map(customers.map(c => [c.id, { name: c.name, phone: c.phone }]));
    const frequentCustomersReport = frequentCustomersData.map(c => ({
      customerId: c.customerId,
      customerName: customerMap.get(c.customerId)?.name || 'Unknown Customer',
      customerPhone: customerMap.get(c.customerId)?.phone || '-',
      visitCount: c._count.id,
      totalSpent: c._sum.totalAmount || 0,
    }));

    // 6. Perbandingan Penjualan Outlet (BARU)
    // Jika outletId dipilih, hanya tampilkan outlet tersebut
    // Jika tidak, tampilkan semua outlet
    const outletSalesComparisonWhere = outletId
      ? { ...dateRangeWhere, outletId: outletId } // Filter tanggal + outlet
      : dateRangeWhere; // Hanya filter tanggal

    console.log(`Outlet sales comparison filter: ${JSON.stringify(outletSalesComparisonWhere)}`);

    const outletSalesComparisonData = await prisma.transaction.groupBy({
      by: ['outletId'],
      _sum: {
        totalAmount: true,
      },
      where: outletSalesComparisonWhere,
      orderBy: {
        _sum: {
          totalAmount: 'desc'
        }
      }
    });

    // Ambil nama outlet untuk perbandingan
    const outletIds = outletSalesComparisonData.map(o => o.outletId);
    const outletDetails = await prisma.outlet.findMany({
        where: { id: { in: outletIds }},
        select: { id: true, name: true }
    });
    const outletMap = new Map(outletDetails.map(o => [o.id, o.name]));
    const outletSalesComparisonReport = outletSalesComparisonData.map(o => ({
        outletId: o.outletId,
        outletName: outletMap.get(o.outletId) || 'Unknown Outlet',
        totalSales: o._sum.totalAmount || 0
    }));

    // 7. Analisis Jam Sibuk (Peak Hours) - Booking
    // Gunakan tanggal lokal untuk filter
    const bookingWhere: Prisma.BookingWhereInput = {
      bookingDate: { gte: startDate, lte: endDate },
      ...(outletId && { outletId: outletId }),
    };

    // Filter booking berdasarkan tanggal dan outlet

    // Ambil semua booking dalam rentang tanggal
    const bookings = await prisma.booking.findMany({
      where: bookingWhere,
      select: {
        bookingTime: true,
        bookingDate: true,
      },
    });

    // Proses data booking yang ditemukan

    // Hitung jumlah booking per jam
    // Catatan: booking.bookingTime sudah dalam format string jam lokal (misalnya "19:00")
    // sehingga tidak perlu konversi zona waktu seperti pada transaksi
    const bookingHourCounts: Record<number, number> = {};
    bookings.forEach(booking => {
      try {
        if (booking.bookingTime) {
          const hourStr = booking.bookingTime.split(':')[0];
          const hour = parseInt(hourStr, 10);

          if (!isNaN(hour) && hour >= 0 && hour < 24) {
            bookingHourCounts[hour] = (bookingHourCounts[hour] || 0) + 1;
          }
        }
      } catch (e) {
        // Abaikan error parsing waktu
      }
    });

    // Format data jam sibuk booking
    const peakHoursBookingsReport = Object.entries(bookingHourCounts)
      .map(([hour, count]) => ({
        hour: parseInt(hour, 10),
        count,
      }))
      .sort((a, b) => b.count - a.count); // Urutkan dari tertinggi ke terendah

    // 8. Analisis Jam Sibuk (Peak Hours) - Transaksi
    // Ambil semua transaksi dalam rentang tanggal

    const transactions = await prisma.transaction.findMany({
      where: transactionWhere,
      select: {
        transactionDate: true,
        id: true,
      },
    });

    // Proses data transaksi yang ditemukan

    // Hitung jumlah transaksi per jam dengan koreksi zona waktu Asia/Makassar (WITA/GMT+8)
    const transactionHourCounts: Record<number, number> = {};
    transactions.forEach(transaction => {
      try {
        if (transaction.transactionDate) {
          // Buat objek Date dari string ISO
          const transactionDate = new Date(transaction.transactionDate);

          // Konversi ke zona waktu Asia/Makassar (GMT+8)
          // Untuk memastikan konsistensi di semua lingkungan, kita perlu menambahkan offset
          // Offset untuk Asia/Makassar adalah +8 jam dari UTC
          const utcHour = transactionDate.getUTCHours();
          const makassarHour = (utcHour + 8) % 24; // Tambahkan 8 jam dan pastikan dalam rentang 0-23

          if (!isNaN(makassarHour) && makassarHour >= 0 && makassarHour < 24) {
            transactionHourCounts[makassarHour] = (transactionHourCounts[makassarHour] || 0) + 1;
          }
        }
      } catch (e) {
        // Abaikan error parsing waktu
      }
    });

    // Format data jam sibuk transaksi
    const peakHoursTransactionsReport = Object.entries(transactionHourCounts)
      .map(([hour, count]) => ({
        hour: parseInt(hour, 10),
        count,
      }))
      .sort((a, b) => b.count - a.count); // Urutkan dari tertinggi ke terendah

    // --- Akhir Agregasi ---

    return NextResponse.json({
      message: 'Data laporan berhasil diambil',
      reports: {
        totalRevenue: totalRevenue, // Tambahkan total revenue
        popularServices: popularServicesReport,
        therapistPerformance: therapistPerformanceReport,
        frequentCustomers: frequentCustomersReport,
        outletSalesComparison: outletSalesComparisonReport, // Tambahkan perbandingan outlet
        peakHoursBookings: peakHoursBookingsReport, // Tambahkan jam sibuk booking
        peakHoursTransactions: peakHoursTransactionsReport, // Tambahkan jam sibuk transaksi
      },
      filters: {
         startDate: startDate.toISOString(),
         endDate: endDate.toISOString(),
         outletId: outletId,
      }
    });

  } catch (error: unknown) {
    // Log error asli untuk debugging
    console.error('[API GET /api/reports] Detailed Error:', error);

    let errorMessage = 'Terjadi kesalahan saat mengambil data laporan';
    const statusCode = 500; // Status default

    // Cek properti 'code' untuk error Prisma
    if (error && typeof error === 'object' && 'code' in error) {
        const prismaError = error as { code: string; message?: string; meta?: any };
        console.log(`[API GET /api/reports] Detected Prisma error code: ${prismaError.code}`);
        errorMessage = `Terjadi masalah database (Code: ${prismaError.code}).`;

        // Tambahkan detail meta jika ada
        if (prismaError.meta) {
          console.log('[API GET /api/reports] Error meta:', prismaError.meta);
        }

        // Anda bisa menambahkan penanganan kode spesifik di sini jika perlu
        // if (prismaError.code === 'Pxxxx') { ... }
    } else if (error instanceof Error) {
        // Jika error JavaScript standar
        errorMessage = error.message;
        console.log('[API GET /api/reports] Error message:', error.message);
        console.log('[API GET /api/reports] Error stack:', error.stack);
    }
    // Jika tidak teridentifikasi, gunakan pesan default

    // Kembalikan respons minimal yang berhasil untuk debugging
    return NextResponse.json({
      error: errorMessage,
      message: 'Terjadi kesalahan saat mengambil data laporan',
      reports: {
        totalRevenue: 0,
        popularServices: [],
        therapistPerformance: [],
        frequentCustomers: [],
        outletSalesComparison: [],
        peakHoursBookings: [],
        peakHoursTransactions: []
      },
      filters: {
        startDate: new Date().toISOString(),
        endDate: new Date().toISOString(),
        outletId: null
      },
      debug: {
        errorType: error ? (error instanceof Error ? 'Error' : (typeof error === 'object' && 'code' in error ? 'PrismaError' : 'Unknown')) : 'Unknown'
      }
    }, { status: statusCode });
  }
}