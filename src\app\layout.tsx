import { Inter } from "next/font/google"
import "./globals.css"
import { OutletProvider } from "@/contexts/OutletContext"
import { AuthProvider } from "@/contexts/AuthContext"
import { ThemeProvider } from "@/contexts/ThemeContext"
import { SessionManager } from "@/components/auth/SessionManager"
import { Toaster } from 'react-hot-toast'
import { Metadata } from "next"

const inter = Inter({ subsets: ["latin"] })

export const metadata: Metadata = {
  title: "Breaktime Dashboard",
  description: "Sistem manajemen spa dan wellness",
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="id">
      <head>
        <meta charSet="utf-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <link rel="icon" href="/favicon.ico" />
        {/* Tambahan meta tags untuk PWA jika diperlukan */}
        <meta name="theme-color" content="#14b8a6" />
        <meta name="apple-mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-status-bar-style" content="default" />
        <meta name="apple-mobile-web-app-title" content="Breaktime" />
        
        {/* Preconnect untuk performa */}
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
        
        {/* Meta untuk SEO */}
        <meta name="robots" content="noindex, nofollow" />
        <meta name="description" content="Sistem manajemen spa dan wellness Breaktime" />
        
        {/* Open Graph untuk social sharing */}
        <meta property="og:title" content="Breaktime Dashboard" />
        <meta property="og:description" content="Sistem manajemen spa dan wellness" />
        <meta property="og:type" content="website" />
        
        {/* Favicon dan app icons */}
        <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />
        <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png" />
        <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png" />
        <link rel="manifest" href="/site.webmanifest" />
        
        {/* Preload critical resources */}
        <link rel="preload" href="/fonts/inter-var.woff2" as="font" type="font/woff2" crossOrigin="anonymous" />
      </head>
      <body className={`${inter.className} antialiased`}>
        {/* Wrapper untuk providers */}
        <div id="root" className="min-h-screen">
          {/* Auth Provider - Level tertinggi untuk autentikasi */}
          <AuthProvider>
            {/* Theme Provider - Untuk manajemen tema aplikasi */}
            <ThemeProvider>
              {/* Session Manager - Menangani session expired secara global */}
              <SessionManager />
              
              {/* Outlet Provider - Untuk manajemen outlet yang dipilih */}
              <OutletProvider>
                {/* Main content */}
                <main className="min-h-screen">
                  {children}
                </main>
              </OutletProvider>
            </ThemeProvider>
          </AuthProvider>
        </div>
        
        {/* Toast notifications */}
        <Toaster
          position="top-center"
          reverseOrder={false}
          gutter={8}
          containerClassName=""
          containerStyle={{}}
          toastOptions={{
            // Default options untuk semua toast
            duration: 4000,
            style: {
              background: '#363636',
              color: '#fff',
            },
            // Default options untuk success toast
            success: {
              duration: 3000,
              style: {
                background: '#10b981',
              },
            },
            // Default options untuk error toast
            error: {
              duration: 5000,
              style: {
                background: '#ef4444',
              },
            },
          }}
        />
        
        {/* Portal untuk modal */}
        <div id="modal-root"></div>
        
        {/* Theme akan diatur oleh ThemeProvider di client-side */}
      </body>
    </html>
  )
}
