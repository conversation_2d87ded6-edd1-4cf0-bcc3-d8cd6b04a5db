'use client';

import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { motion } from 'framer-motion';
import {
  FiPlus, FiSearch, FiEdit, FiTrash2, FiList,
  FiInbox, FiCheck, FiX, FiPhone, FiMapPin,
  FiDownload, FiFilter, FiUsers, FiCalendar,
  FiGitMerge, FiAlertTriangle
} from 'react-icons/fi';
import { format, isValid } from 'date-fns';
import { id as idLocale } from 'date-fns/locale';
import { toast } from 'sonner';
import * as XLSX from 'xlsx';
import PermissionGuard from '@/components/permission/PermissionGuard';
import { useAuth } from '@/contexts/AuthContext';

// --- Interfaces ---
interface Therapist {
  id: string;
  name: string;
  phone?: string | null;
  outletId: string;
  isActive: boolean;
  createdAt: string;
  captainUserId?: string | null;
  outlet?: { id: string; name: string };
  captain?: { id: string; name: string };
}

interface Outlet {
  id: string;
  name: string;
}

// Interface Transaksi (Perbaiki agar konsisten dengan API)
interface TransactionReceiptData {
  outletName?: string;
  transactionId: string;
  id?: number | string;         // ID dari database
  displayId?: string;          // ID yang ditampilkan ke user
  dateTime?: string;           // Format tanggal lama (fallback)
  transactionDate?: string;    // Format tanggal baru dari API
  createdAt?: string;          // Tanggal pembuatan dari database
  customerName?: string;       // Nama pelanggan (fallback)
  customer?: { name: string; }; // Relasi ke customer dari API
  therapistName?: string;      // Nama terapis (fallback)
  therapist?: { id: string; name: string }; // Relasi terapis dari API
  outlet?: { id: string; name: string }; // Relasi outlet dari API
  
  // Items structure - prioritaskan struktur dari API
  items?: { name: string; price: number; quantity?: number }[]; // Struktur lama/fallback
  transactionItems?: { 
    quantity: number;
    price: number;
    service: { name: string };
  }[]; // Struktur dari API
  
  // Financial fields
  subtotal?: number;           // Untuk fallback
  discountType?: 'percentage' | 'fixed' | 'none';
  discountValue?: number;
  discountAmount?: number;
  additionalCharge?: number;
  tax?: number;
  total?: number;              // Untuk fallback
  totalAmount?: number;        // Field total dari API (prioritas)
  paymentMethod?: string;
  note?: string;
  notes?: string;              // Field notes dari API
  therapistCommissionEarned?: number; // Komisi aktual yg didapat (Rp), bisa diedit
}
// --- End Interfaces ---

// Helper Functions untuk Perhitungan Konsisten

// Menghitung harga asli layanan (SEBELUM diskon) - Ini untuk penjualan terapis
const getOriginalServiceTotal = (trx: TransactionReceiptData): number => {
  let total = 0;
  
  // Prioritaskan transactionItems dari API
  if (Array.isArray(trx.transactionItems) && trx.transactionItems.length > 0) {
    total = trx.transactionItems.reduce((sum, item) => {
      return sum + (item.price * item.quantity);
    }, 0);
  } 
  // Fallback ke items structure lama
  else if (Array.isArray(trx.items) && trx.items.length > 0) {
    total = trx.items.reduce((sum, item) => {
      return sum + (item.price * (item.quantity || 1));
    }, 0);
  }
  // Fallback terakhir: jika ada totalAmount + discountAmount, hitung harga asli
  else {
    const finalTotal = trx.totalAmount ?? trx.total ?? 0;
    const discountAmount = trx.discountAmount ?? 0;
    
    // Jika ada diskon, tambahkan kembali untuk mendapatkan harga asli
    total = finalTotal + discountAmount;
    
    // Kurangi biaya tambahan untuk mendapatkan harga layanan saja
    if (trx.additionalCharge && trx.additionalCharge > 0) {
      total -= trx.additionalCharge;
    }
  }
  
  return total;
};

// Menghitung total penjualan terapis (harga asli layanan + biaya tambahan, tanpa dikurangi diskon)
const getTherapistSalesTotal = (trx: TransactionReceiptData): number => {
  let total = getOriginalServiceTotal(trx);
  
  // Tambahkan biaya tambahan jika ada (biaya tambahan masuk ke penjualan terapis)
  if (trx.additionalCharge && trx.additionalCharge > 0) {
    total += trx.additionalCharge;
  }
  
  return total;
};

// DEPRECATED: Gunakan getOriginalServiceTotal sebagai gantinya
const getTransactionTotal = (trx: TransactionReceiptData): number => {
  return getOriginalServiceTotal(trx);
};

// DEPRECATED: Gunakan getTherapistSalesTotal sebagai gantinya  
const getTransactionTotalWithCharges = (trx: TransactionReceiptData): number => {
  return getTherapistSalesTotal(trx);
};

const getServiceNamesFromTransaction = (trx: TransactionReceiptData): string => {
  // Prioritaskan transactionItems dari API
  if (Array.isArray(trx.transactionItems) && trx.transactionItems.length > 0) {
    return trx.transactionItems.map(item => {
      const serviceName = item.service?.name || 'Layanan tidak diketahui';
      return item.quantity > 1 ? `${serviceName} (${item.quantity}x)` : serviceName;
    }).join(', ');
  }
  // Fallback ke items structure lama
  else if (Array.isArray(trx.items) && trx.items.length > 0) {
    return trx.items.map(item => {
      const quantity = item.quantity || 1;
      return quantity > 1 ? `${item.name} (${quantity}x)` : item.name;
    }).join(', ');
  }
  
  return '-';
};

const getCustomerName = (trx: TransactionReceiptData): string => {
  return trx.customer?.name || trx.customerName || 'Pelanggan Umum';
};

const getTransactionDate = (trx: TransactionReceiptData): string => {
  const dateStr = trx.transactionDate || trx.createdAt || trx.dateTime;
  if (!dateStr) return '-';
  
  try {
    return new Date(dateStr).toLocaleDateString('id-ID');
  } catch {
    return dateStr;
  }
};

// Hapus Mock Data Awal Terapis
// const initialMockTherapists: Omit<Therapist, 'id' | 'createdAt' | 'outlet'>[] = [ ... ];

// Hapus Kunci localStorage
// const THERAPIST_DATA_KEY = 'therapistData';
const TRANSACTION_HISTORY_KEY = 'transactionHistory'; // Biarkan ini jika masih dipakai untuk fallback riwayat trx

// Format tanggal untuk tampilan
const formatDateDisplay = (date: Date | null): string => {
  if (!date || !isValid(date)) return '';
  return format(date, 'dd MMMM yyyy', { locale: idLocale });
};

// Format tanggal untuk API
const formatDateForAPI = (date: Date): string => {
  return format(date, 'yyyy-MM-dd');
};

// Varian Animasi
const fadeInUp = { hidden: { opacity: 0, y: 20 }, visible: { opacity: 1, y: 0, transition: { duration: 0.5 } } };
const staggerContainer = { hidden: { opacity: 0 }, visible: { opacity: 1, transition: { staggerChildren: 0.1 } } };

export default function TherapistPage() {
  const { user } = useAuth();
  const [therapists, setTherapists] = useState<Therapist[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');

  // State untuk daftar outlet (untuk dropdown)
  const [outlets, setOutlets] = useState<Outlet[]>([]);
  const [isLoadingOutlets, setIsLoadingOutlets] = useState(true);

  // State Modal Add/Edit Terapis
  const [isAddEditModalOpen, setIsAddEditModalOpen] = useState(false);
  const [selectedTherapistForEdit, setSelectedTherapistForEdit] = useState<Therapist | null>(null);
  const [formDataTherapist, setFormDataTherapist] = useState<Omit<Therapist, 'id' | 'createdAt' | 'outlet'> & { specialty?: string }>({
    name: '',
    phone: '',
    outletId: '',
    isActive: true,
    captainUserId: null, // Tambahkan field untuk kapten
    specialty: ''
  });

  // State untuk menyimpan daftar kapten
  const [captains, setCaptains] = useState<{ id: string; name: string }[]>([]);

  // State Modal Detail Riwayat
  const [isHistoryModalOpen, setIsHistoryModalOpen] = useState(false);
  const [selectedTherapistForHistory, setSelectedTherapistForHistory] = useState<Therapist | null>(null);
  const [therapistTransactionHistory, setTherapistTransactionHistory] = useState<TransactionReceiptData[]>([]);
  const [allTherapistTransactions, setAllTherapistTransactions] = useState<TransactionReceiptData[]>([]);
  const [isHistoryLoading, setIsHistoryLoading] = useState(false);
  const [historyStartDate, setHistoryStartDate] = useState<Date>(new Date(new Date().getFullYear(), new Date().getMonth(), 1)); // Awal bulan ini
  const [historyEndDate, setHistoryEndDate] = useState<Date>(new Date()); // Hari ini

  // State Modal Edit Komisi
  const [isCommissionEditModalOpen, setIsCommissionEditModalOpen] = useState(false);
  const [selectedTrxForCommissionEdit, setSelectedTrxForCommissionEdit] = useState<TransactionReceiptData | null>(null);
  const [editedCommissionValue, setEditedCommissionValue] = useState<number>(0);

  // --> State untuk Modal Konfirmasi Hapus Terapis <--
  const [isDeleteTherapistModalOpen, setIsDeleteTherapistModalOpen] = useState(false);
  const [therapistToDelete, setTherapistToDelete] = useState<Therapist | null>(null);

  // --> State untuk Modal Penggabungan Terapis <--
  const [isMergeModalOpen, setIsMergeModalOpen] = useState(false);
  const [primaryTherapist, setPrimaryTherapist] = useState<Therapist | null>(null);
  const [secondaryTherapist, setSecondaryTherapist] = useState<Therapist | null>(null);
  const [secondaryTherapistSearchTerm, setSecondaryTherapistSearchTerm] = useState('');
  const [isMerging, setIsMerging] = useState(false);
  const [mergeStats, setMergeStats] = useState<{
    transactionsCount: number;
    bookingsCount: number;
    commissionsCount: number;
    attendancesCount: number;
  } | null>(null);

  // --- Fungsi Fetch Terapis (Diekstrak) ---
  const fetchTherapists = useCallback(async () => {
    setIsLoading(true);
    try {
      // Tambahkan parameter include untuk mengambil relasi outlet dan captain
      const response = await fetch('/api/therapists?include=outlet,captain');
      if (!response.ok) throw new Error(`Error: ${response.status}`);
      const data = await response.json();
      // Data terapis berhasil diambil
      setTherapists(data.therapists || []);
    } catch (error) {
      console.error("Gagal load terapis:", error);
      setTherapists([]);
      toast.error("Gagal memuat data terapis."); // Notifikasi error fetch
      // Hapus fallback localStorage karena tidak konsisten dengan API
    } finally {
      setIsLoading(false);
    }
  }, []); // Dependency array kosong jika tidak bergantung pada state/props lain

  // Load Therapist Data dari API saat mount
  useEffect(() => {
    fetchTherapists();
  }, [fetchTherapists]); // Panggil fungsi yg diekstrak

  // Load Outlet Data dari API
  useEffect(() => {
    const fetchOutlets = async () => {
      setIsLoadingOutlets(true);
      try {
        const response = await fetch('/api/outlets');
        if (!response.ok) throw new Error(`Error fetching outlets: ${response.status}`);
        const data = await response.json();
        setOutlets(data.outlets || []);
      } catch (error) {
        console.error("Gagal load outlets:", error);
        setOutlets([]);
      } finally {
        setIsLoadingOutlets(false);
      }
    };
    fetchOutlets();
  }, []);

  // Load Kapten Data dari API
  useEffect(() => {
    const fetchCaptains = async () => {
      try {
        // Ambil user dengan role STAFF dan isCaptain=true
        const response = await fetch('/api/users?role=STAFF&isCaptain=true');
        if (!response.ok) throw new Error(`Error fetching captains: ${response.status}`);
        const data = await response.json();

        // Ambil hanya id dan name dari setiap kapten
        const captainsList = (data.users || []).map((user: { id: string; name: string }) => ({
          id: user.id,
          name: user.name
        }));

        setCaptains(captainsList);
      } catch (error) {
        console.error("Gagal load kapten:", error);
        setCaptains([]);
      }
    };
    fetchCaptains();
  }, []);

  // Filter Therapists
  const filteredTherapists = useMemo(() => {
    if (!searchTerm) return therapists;
    const lowerSearch = searchTerm.toLowerCase();
    return therapists.filter(t =>
      t.name.toLowerCase().includes(lowerSearch) ||
      t.phone?.toLowerCase().includes(lowerSearch) ||
      t.outlet?.name.toLowerCase().includes(lowerSearch)
    );
  }, [therapists, searchTerm]);

  // --- Handlers Add/Edit Terapis ---
  const handleOpenAddEditModal = (therapist: Therapist | null = null) => {
    setSelectedTherapistForEdit(therapist);
    if (therapist) {
      setFormDataTherapist({
        name: therapist.name ?? '',
        phone: therapist.phone ?? '',
        outletId: therapist.outletId ?? '',
        isActive: therapist.isActive ?? true,
        captainUserId: therapist.captainUserId || null, // Tambahkan captainUserId
        specialty: '' // Abaikan
      });
    } else {
      setFormDataTherapist({
        name: '',
        phone: '',
        outletId: outlets[0]?.id || '',
        isActive: true,
        captainUserId: null, // Default tidak ada kapten
        specialty: ''
      });
    }
    setIsAddEditModalOpen(true);
  };
  const handleCloseAddEditModal = () => setIsAddEditModalOpen(false);

  const handleInputChangeTherapist = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;
    const newValue = type === 'checkbox' ? (e.target as HTMLInputElement).checked : value;
    setFormDataTherapist(prev => ({ ...prev, [name]: newValue }));
  };

  const handleSaveTherapist = async () => {
    if (!formDataTherapist.name) return alert('Nama Terapis wajib diisi.');
    if (!formDataTherapist.outletId) return alert('Lokasi Outlet wajib dipilih.'); // Validasi outlet
    if (formDataTherapist.phone && !/^[0-9]{10,}$/.test(formDataTherapist.phone)) return alert('Format No. Telepon tidak valid.'); // Validasi telepon opsional

    // Siapkan data untuk dikirim (tanpa specialty)
    const dataToSend = {
      name: formDataTherapist.name,
      phone: formDataTherapist.phone || null, // Kirim null jika kosong
      outletId: formDataTherapist.outletId,
      isActive: formDataTherapist.isActive,
      captainUserId: formDataTherapist.captainUserId, // Tambahkan captainUserId
      // experience: ... // Jika ingin edit experience, tambahkan di sini
    };

    setIsLoading(true);
    try {
      let response;
      if (selectedTherapistForEdit) { // Edit Mode
        response = await fetch(`/api/therapists/${selectedTherapistForEdit.id}`, {
          method: 'PUT',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(dataToSend),
        });
      } else { // Add Mode
        response = await fetch('/api/therapists', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(dataToSend),
        });
      }

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({})); // Tangkap error body jika ada
        throw new Error(`Error ${response.status}: ${errorData.error || response.statusText}`);
      }

      const result = await response.json();
      const savedTherapist = result.therapist;

      // Update state lokal dengan data dari backend (termasuk relasi outlet dan captain jika ada)
      if (selectedTherapistForEdit) {
        setTherapists(prev => prev.map(t => t.id === savedTherapist.id ? savedTherapist : t));
      } else {
        setTherapists(prev => [...prev, savedTherapist]);
      }

      // Refresh data terapis untuk memastikan relasi captain diambil dengan benar
      fetchTherapists();

      handleCloseAddEditModal();
      toast.success(result.message || `Terapis ${selectedTherapistForEdit ? 'berhasil diupdate' : 'berhasil ditambahkan'}.`);

    } catch (error) {
      console.error('Error saving therapist:', error);
      const errorMessage = error instanceof Error ? error.message : 'Gagal menyimpan data terapis.';
      toast.error(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  // --- Handlers untuk Modal Hapus Terapis ---
  const handleOpenDeleteTherapistModal = (therapist: Therapist) => {
    setTherapistToDelete(therapist);
    setIsDeleteTherapistModalOpen(true);
  };

  const handleCloseDeleteTherapistModal = () => {
    setTherapistToDelete(null);
    setIsDeleteTherapistModalOpen(false);
  };

  const confirmDeleteTherapist = async () => {
    if (!therapistToDelete) return;

    setIsLoading(true);
    const therapistId = therapistToDelete.id;
    const therapistName = therapistToDelete.name;
    handleCloseDeleteTherapistModal(); // Tutup modal

    try {
      const response = await fetch(`/api/therapists/${therapistId}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Gagal menghapus terapis');
      }

      // Update state lokal HAPUS (fetch ulang lebih baik)
      // const updated = therapists.filter(t => t.id !== therapistId);
      // setTherapists(updated);
      await fetchTherapists(); // <-- Panggil fungsi fetch yg sudah diekstrak

      toast.success(`Terapis "${therapistName}" berhasil dihapus.`);
    } catch (err: unknown) {
      console.error('Error deleting therapist:', err);
      toast.error(`Gagal menghapus terapis: ${err instanceof Error ? err.message : 'Terjadi kesalahan'}`);
    } finally {
      setIsLoading(false);
    }
  };
  // --- Akhir Handlers Modal Hapus Terapis ---

  // Modifikasi handleDeleteTherapist: Hanya buka modal
  const handleDeleteTherapist = (therapist: Therapist) => {
    handleOpenDeleteTherapistModal(therapist);
  };

  // --- Handlers Modal Penggabungan Terapis ---
  const handleOpenMergeModal = (therapist: Therapist) => {
    setPrimaryTherapist(therapist);
    setSecondaryTherapist(null);
    setSecondaryTherapistSearchTerm('');
    setMergeStats(null);
    setIsMergeModalOpen(true);
  };

  const handleCloseMergeModal = () => {
    setIsMergeModalOpen(false);
    setPrimaryTherapist(null);
    setSecondaryTherapist(null);
    setSecondaryTherapistSearchTerm('');
    setMergeStats(null);
  };

  const handleSelectSecondaryTherapist = (therapist: Therapist) => {
    setSecondaryTherapist(therapist);
    setSecondaryTherapistSearchTerm('');
  };

  const handleConfirmMerge = async () => {
    if (!primaryTherapist || !secondaryTherapist) {
      toast.error('Pilih terapis utama dan terapis yang akan digabungkan terlebih dahulu.');
      return;
    }

    if (primaryTherapist.id === secondaryTherapist.id) {
      toast.error('Tidak dapat menggabungkan terapis yang sama.');
      return;
    }

    if (primaryTherapist.outletId !== secondaryTherapist.outletId) {
      toast.error(`Terapis harus berasal dari outlet yang sama. ${primaryTherapist.outlet?.name} ≠ ${secondaryTherapist.outlet?.name}`);
      return;
    }

    setIsMerging(true);

    try {
      const response = await fetch('/api/therapists/merge', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          primaryTherapistId: primaryTherapist.id,
          secondaryTherapistId: secondaryTherapist.id,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Gagal menggabungkan terapis');
      }

      const result = await response.json();

      // Simpan statistik penggabungan
      setMergeStats(result.stats);

      // Refresh data terapis
      await fetchTherapists();

      toast.success(`Terapis "${secondaryTherapist.name}" berhasil digabungkan ke "${primaryTherapist.name}".`);
    } catch (error) {
      console.error('Error merging therapists:', error);
      toast.error(`Gagal menggabungkan terapis: ${error instanceof Error ? error.message : 'Terjadi kesalahan'}`);
    } finally {
      setIsMerging(false);
    }
  };
  // --- Akhir Handlers Modal Penggabungan Terapis ---

  // --- Handlers Riwayat & Komisi ---
  const handleOpenHistoryModal = async (therapist: Therapist) => {
    setSelectedTherapistForHistory(therapist);
    setIsHistoryModalOpen(true);
    setIsHistoryLoading(true);

    // Buka modal riwayat untuk terapis

    try {
      // Ambil riwayat transaksi dari API
      const response = await fetch(`/api/transactions?therapistId=${therapist.id}`);

      if (!response.ok) {
        throw new Error(`Error: ${response.status}`);
      }

      const data = await response.json();
      const transactions = data.transactions || [];

      // Periksa apakah data transaksi memiliki properti additionalCharge
      if (transactions.length > 0) {
        // Jika tidak ada additionalCharge, ambil data lengkap dari API transaksi
        const transactionsWithDetails = await Promise.all(
          transactions.map(async (trx: TransactionReceiptData) => {
            // Jika tidak ada additionalCharge, ambil detail dari API
            if (!trx.hasOwnProperty('additionalCharge')) {
              try {
                const detailResponse = await fetch(`/api/transactions/${trx.id || trx.transactionId}`);
                if (detailResponse.ok) {
                  const detailData = await detailResponse.json();
                  return {
                    ...trx,
                    additionalCharge: detailData.additionalCharge || 0,
                    discountType: detailData.discountType || 'none',
                    discountValue: detailData.discountValue || 0,
                    discountAmount: detailData.discountAmount || 0
                  };
                }
              } catch (err) {
                console.error(`Error fetching details for transaction ${trx.id || trx.transactionId}:`, err);
              }
            }
            return trx;
          })
        );

        // Simpan semua transaksi dengan detail tambahan
        setAllTherapistTransactions(transactionsWithDetails || []);

        // Terapkan filter tanggal
        filterTransactionsByDate(transactionsWithDetails || []);
      } else {
        // Jika tidak ada transaksi, gunakan data asli
        setAllTherapistTransactions(transactions);
        filterTransactionsByDate(transactions);
      }
    } catch (error) {
      console.error("Gagal load riwayat trx terapis dari API:", error);

      // Fallback ke localStorage jika API gagal
      try {
        const allHistory = JSON.parse(localStorage.getItem(TRANSACTION_HISTORY_KEY) || '[]');
        // Filter transaksi berdasarkan nama terapis (case insensitive)
        const history = allHistory.filter((trx: TransactionReceiptData) =>
          trx.therapistName?.toLowerCase() === therapist.name.toLowerCase()
        ).reverse();

        // Simpan semua transaksi
        setAllTherapistTransactions(history);

        // Terapkan filter tanggal
        filterTransactionsByDate(history);
      } catch (fallbackError) {
        console.error("Gagal load riwayat dari localStorage:", fallbackError);
        setAllTherapistTransactions([]);
        setTherapistTransactionHistory([]);
      }
    } finally {
      setIsHistoryLoading(false);
    }
  };

  // Fungsi untuk memfilter transaksi berdasarkan tanggal
  const filterTransactionsByDate = (transactions: TransactionReceiptData[]) => {
    if (!Array.isArray(transactions)) {
      setTherapistTransactionHistory([]);
      return;
    }

    // Konversi tanggal filter ke objek Date dengan waktu 00:00:00 dan 23:59:59
    const startDateObj = new Date(historyStartDate);
    startDateObj.setHours(0, 0, 0, 0);

    const endDateObj = new Date(historyEndDate);
    endDateObj.setHours(23, 59, 59, 999);

    // Filter transaksi berdasarkan tanggal
    const filteredTransactions = transactions.filter(trx => {
      // Ambil tanggal transaksi dari berbagai kemungkinan field
      const trxDateStr = trx.transactionDate || trx.dateTime || trx.createdAt || '';
      if (!trxDateStr) return false;

      try {
        // Parse tanggal transaksi
        const trxDate = new Date(trxDateStr);

        // Periksa apakah tanggal transaksi berada dalam rentang filter
        return trxDate >= startDateObj && trxDate <= endDateObj;
      } catch {
        // Error parsing transaction date
        return false;
      }
    });

    // Update state dengan transaksi yang sudah difilter
    setTherapistTransactionHistory(filteredTransactions);
  };
  const handleCloseHistoryModal = () => setIsHistoryModalOpen(false);

  // Fungsi untuk menangani perubahan tanggal mulai
  const handleHistoryStartDateChange = (date: Date) => {
    setHistoryStartDate(date);
    filterTransactionsByDate(allTherapistTransactions);
  };

  // Fungsi untuk menangani perubahan tanggal akhir
  const handleHistoryEndDateChange = (date: Date) => {
    setHistoryEndDate(date);
    filterTransactionsByDate(allTherapistTransactions);
  };

  const handleOpenCommissionEditModal = (trx: TransactionReceiptData) => {
    // Pastikan nilai komisi yang diedit tidak termasuk biaya tambahan
    setSelectedTrxForCommissionEdit(trx);

    // Gunakan nilai komisi yang sudah disimpan di database
    // Biaya tambahan tidak boleh mempengaruhi nilai komisi
    setEditedCommissionValue(trx.therapistCommissionEarned ?? 0);

    setIsCommissionEditModalOpen(true);
  };
  const handleCloseCommissionEditModal = () => setIsCommissionEditModalOpen(false);

  const handleSaveCommission = async () => {
    const commissionToSave = Number(editedCommissionValue) || 0;
    if (!selectedTrxForCommissionEdit || commissionToSave < 0) return;

    // Gunakan displayId jika ada, jika tidak gunakan transactionId
    const trxIdToUpdate = selectedTrxForCommissionEdit.displayId || selectedTrxForCommissionEdit.transactionId;

    try {
      const response = await fetch(`/api/transactions/${trxIdToUpdate}/commission`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ commission: commissionToSave }),
      });

      if (!response.ok) {
        throw new Error(`Error: ${response.status}`);
      }

      // Fallback: Simpan juga ke localStorage
      try {
        const allHistory: TransactionReceiptData[] = JSON.parse(localStorage.getItem(TRANSACTION_HISTORY_KEY) || '[]');

        const updatedAllHistory = allHistory.map(trx => {
          if (trx.displayId === trxIdToUpdate || trx.transactionId === trxIdToUpdate) {
            return { ...trx, therapistCommissionEarned: commissionToSave };
          }
          return trx;
        });

        localStorage.setItem(TRANSACTION_HISTORY_KEY, JSON.stringify(updatedAllHistory));
      } catch (localStorageError) {
        console.error("Gagal update localStorage:", localStorageError);
      }

      // Update state riwayat Terapis yang sedang ditampilkan di modal
      setTherapistTransactionHistory(prevHistory =>
        prevHistory.map(trx =>
          (trx.displayId === trxIdToUpdate || trx.transactionId === trxIdToUpdate)
            ? { ...trx, therapistCommissionEarned: commissionToSave }
            : trx
        )
      );

      toast.success(`Komisi untuk transaksi ${trxIdToUpdate} diubah menjadi Rp ${commissionToSave.toLocaleString('id-ID')}.`);
      handleCloseCommissionEditModal(); // Tutup modal edit komisi kecil

    } catch {
      // Gagal simpan komisi
      alert("Gagal menyimpan perubahan komisi.");
    }
  };

  // Hitung total komisi untuk footer
  const totalCommission = useMemo(() => {
    if (!Array.isArray(therapistTransactionHistory)) return 0;

    // Pastikan komisi tidak termasuk biaya tambahan
    return therapistTransactionHistory.reduce((sum, trx) => {
      // Gunakan nilai komisi yang sudah disimpan di database
      // Biaya tambahan tidak boleh mempengaruhi nilai komisi
      return sum + (trx.therapistCommissionEarned ?? 0);
    }, 0);
  }, [therapistTransactionHistory]);

  // Fungsi untuk mengunduh data terapis dalam format Excel
  const handleDownloadExcel = async () => {
    // Jika tidak ada data, tampilkan pesan
    if (!Array.isArray(therapists) || therapists.length === 0) {
      toast.error('Tidak ada data terapis untuk diunduh.');
      return;
    }

    try {
      // Fungsi untuk format Rupiah
      const formatRupiah = (amount: number) => {
        return `Rp ${amount.toLocaleString('id-ID')}`;
      };

      // Fungsi untuk format tanggal
      const formatDate = (dateString: string) => {
        if (!dateString) return '-';
        try {
          const date = new Date(dateString);
          return date.toLocaleDateString('id-ID', { day: '2-digit', month: '2-digit', year: 'numeric' });
        } catch {
          return dateString; // Kembalikan string asli jika parsing gagal
        }
      };

      // Siapkan data untuk Excel - data terapis
      const therapistData = therapists.map(t => ({
        'ID Terapis': t.id,
        'Nama Terapis': t.name,
        'No. Telepon': t.phone || '-',
        'Outlet': t.outlet?.name || '-',
        'Status': t.isActive ? 'Aktif' : 'Nonaktif',
      }));

      // Buat workbook baru
      const wb = XLSX.utils.book_new();

      // Buat worksheet untuk data terapis
      const wsTherapists = XLSX.utils.json_to_sheet(therapistData);
      XLSX.utils.book_append_sheet(wb, wsTherapists, 'Daftar Terapis');

      // Jika ada filter pencarian, buat sheet terpisah untuk hasil filter
      if (searchTerm && filteredTherapists.length > 0 && filteredTherapists.length < therapists.length) {
        const filteredData = filteredTherapists.map(t => ({
          'ID Terapis': t.id,
          'Nama Terapis': t.name,
          'No. Telepon': t.phone || '-',
          'Outlet': t.outlet?.name || '-',
          'Status': t.isActive ? 'Aktif' : 'Nonaktif',
        }));

        const wsFiltered = XLSX.utils.json_to_sheet(filteredData);
        XLSX.utils.book_append_sheet(wb, wsFiltered, `Filter: ${searchTerm}`);
      }

      // Untuk setiap terapis, cek apakah ada riwayat transaksi
      for (const therapist of therapists) {
        // Ambil riwayat transaksi terapis dari API (gunakan fungsi yang sudah ada)
        try {
          const response = await fetch(`/api/transactions?therapistId=${therapist.id}`);
          if (response.ok) {
            const data = await response.json();
            const transactions = data.transactions || [];

            // Jika ada transaksi, buat sheet untuk terapis ini
            if (transactions.length > 0) {
              const transactionData = transactions.map((trx: TransactionReceiptData) => {
                // Pastikan data pelanggan ada
                const customerName = trx.customer?.name || trx.customerName || '-';

                // Format tanggal transaksi - coba beberapa kemungkinan field tanggal
                const transactionDate = trx.transactionDate || trx.dateTime || trx.createdAt || trx.date;
                const formattedDate = formatDate(transactionDate);

                return {
                  'ID Transaksi': trx.displayId || trx.transactionId || trx.id || '-',
                  'Tanggal': formattedDate,
                  'Pelanggan': customerName,
                  'Layanan': Array.isArray(trx.items) ? trx.items.map((i) => i.name).join(', ') :
                            Array.isArray(trx.transactionItems) ? trx.transactionItems.map((i) => i.service?.name || '-').join(', ') : '-',
                  'Total Transaksi': formatRupiah(
                    Array.isArray(trx.items) && trx.items.length > 0
                      ? trx.items.reduce((sum, item) => sum + (item.price * (item.quantity || 1)), 0)
                      : (trx.totalAmount ?? trx.total ?? 0)
                  ),
                  'Komisi': formatRupiah(trx.therapistCommissionEarned ?? 0)
                };
              });

              // Tambahkan total komisi di baris terakhir
              const totalCommission = transactions.reduce((sum: number, trx: TransactionReceiptData) => sum + (trx.therapistCommissionEarned ?? 0), 0);
              transactionData.push({
                'ID Transaksi': '',
                'Tanggal': '',
                'Pelanggan': '',
                'Layanan': 'TOTAL KOMISI',
                'Total Transaksi': '',
                'Komisi': formatRupiah(totalCommission)
              });

              const wsTransactions = XLSX.utils.json_to_sheet(transactionData);

              // Atur lebar kolom
              const wscols = [
                { wch: 15 }, // ID Transaksi
                { wch: 12 }, // Tanggal
                { wch: 20 }, // Pelanggan
                { wch: 30 }, // Layanan
                { wch: 15 }, // Total Transaksi
                { wch: 15 }  // Komisi
              ];
              wsTransactions['!cols'] = wscols;

              XLSX.utils.book_append_sheet(wb, wsTransactions, `Transaksi ${therapist.name.substring(0, 15)}`);
            }
          }
        } catch {
          // Error fetching transactions for therapist
          // Lanjutkan ke terapis berikutnya jika terjadi error
          continue;
        }
      }

      // Unduh file Excel
      const fileName = `Data_Terapis_${new Date().toISOString().split('T')[0]}.xlsx`;
      XLSX.writeFile(wb, fileName);

      toast.success(`File Excel berhasil diunduh: ${fileName}`);
    } catch {
      // Error generating Excel file
      toast.error('Gagal mengunduh data terapis. Silakan coba lagi.');
    }
  };

  if (isLoading) { return <div className="flex justify-center items-center h-screen"><span className="loading loading-spinner loading-lg text-primary"></span></div>; }

  return (
    <motion.div
      className="container mx-auto p-4 md:p-6"
      initial="hidden"
      animate="visible"
      variants={staggerContainer}
    >
      {/* Header & Tombol Tambah */}
      <motion.div variants={fadeInUp} className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6">
        <h1 className="text-2xl md:text-3xl font-bold text-base-content">Kelola Terapis</h1>
        <div className="flex flex-wrap gap-2 w-full sm:w-auto">
          <button
            type="button"
            className="btn btn-outline btn-xs md:btn-sm flex-1 sm:flex-none"
            onClick={handleDownloadExcel}
          >
            <FiDownload className="mr-1"/>
            <span className="hidden xs:inline">Download Excel</span>
            <span className="inline xs:hidden">Excel</span>
          </button>
          <PermissionGuard module="therapists" action="create">
            <button
              type="button"
              className="btn btn-primary btn-xs md:btn-sm flex-1 sm:flex-none"
              onClick={() => handleOpenAddEditModal()}
            >
              <FiPlus className="mr-1"/>
              <span className="hidden xs:inline">Tambah Terapis</span>
              <span className="inline xs:hidden">+ Terapis</span>
            </button>
          </PermissionGuard>
        </div>
      </motion.div>

      {/* Search Bar */}
      <motion.div variants={fadeInUp} className="mb-6">
         <div className="form-control">
            <div className="join w-full sm:w-auto">
               <input
                 type="text"
                 placeholder="Cari Nama Terapis, Outlet..."
                 className="input input-bordered input-xs md:input-sm join-item w-full sm:w-80 placeholder:text-base-content/60 text-base-content"
                 value={searchTerm}
                 onChange={(e) => setSearchTerm(e.target.value)}
                />
               <button
                 type="button"
                 className="btn btn-ghost btn-xs md:btn-sm join-item"
                 aria-label="Cari"
                 title="Cari"
               >
                 <FiSearch className="text-base-content/70"/>
               </button>
            </div>
         </div>
      </motion.div>

      {/* Tabel Terapis untuk layar medium dan di atasnya */}
      <motion.div variants={fadeInUp} className="hidden md:block overflow-x-auto bg-base-100 shadow border border-base-300 rounded-lg">
        <table className="table w-full">
          <thead className="bg-base-200 text-base-content">
            <tr>
              <th>Nama Terapis</th>
              <th>Kontak & Outlet</th>
              <th>Kapten</th>
              <th className="text-center">Status</th>
              <th className="text-center">Aksi</th>
            </tr>
          </thead>
          <tbody>
            {Array.isArray(filteredTherapists) && filteredTherapists.length === 0 && (
               <tr><td colSpan={5} className="text-center text-base-content/70 py-10">
                  <FiInbox className="w-10 h-10 mx-auto mb-2 text-base-content/30"/>
                  {searchTerm ? `Tidak ada terapis cocok dengan "${searchTerm}".` : 'Belum ada data terapis.'}
               </td></tr>
            )}
            {Array.isArray(filteredTherapists) && filteredTherapists.map((therapist) => (
              <tr key={therapist.id} className="hover">
                <td className="font-medium text-base-content">{therapist.name}</td>
                <td className="text-sm text-base-content/80">
                   <div>{therapist.phone || '-'}</div>
                   <div className="text-xs text-base-content/60">{therapist.outlet?.name || therapist.outletId}</div>
                </td>
                <td className="text-sm text-base-content/80">
                   {therapist.captain ? (
                     <div className="flex items-center gap-1">
                       <FiUsers size={14} className="text-secondary" />
                       <span className="text-secondary font-medium">{therapist.captain.name}</span>
                     </div>
                   ) : (
                     <span className="text-base-content/50">-</span>
                   )}
                </td>
                <td className="text-center">
                  {therapist.isActive ?
                      <span className="badge badge-success badge-sm text-success-content"><FiCheck size={12} className="mr-1"/> Aktif</span> :
                      <span className="badge badge-error badge-sm text-error-content"><FiX size={12} className="mr-1"/> Nonaktif</span>}
                </td>
                <td className="text-center space-x-1">
                  {/* Tombol Riwayat & Detail hanya untuk non-STAFF */}
                  <PermissionGuard
                    module="therapists"
                    action="read"
                    fallback={null}
                  >
                    {user?.role !== 'STAFF' && (
                      <button
                        type="button"
                        className="btn btn-xs btn-ghost text-accent tooltip"
                        data-tip="Riwayat & Detail"
                        onClick={() => handleOpenHistoryModal(therapist)}
                        aria-label="Lihat Riwayat dan Detail"
                      >
                        <FiList />
                      </button>
                    )}
                  </PermissionGuard>
                  <PermissionGuard module="therapists" action="update">
                    <button
                      type="button"
                      className="btn btn-xs btn-ghost text-info tooltip"
                      data-tip="Edit Terapis"
                      onClick={() => handleOpenAddEditModal(therapist)}
                      aria-label="Edit Terapis"
                    >
                      <FiEdit />
                    </button>
                  </PermissionGuard>
                  <PermissionGuard module="therapists" action="delete">
                    <button
                      type="button"
                      className="btn btn-xs btn-ghost text-error tooltip"
                      data-tip="Hapus Terapis"
                      onClick={() => handleDeleteTherapist(therapist)}
                      aria-label="Hapus Terapis"
                    >
                        <FiTrash2 />
                    </button>
                  </PermissionGuard>
                  <PermissionGuard module="therapists" action="update">
                    <button
                      type="button"
                      className="btn btn-xs btn-ghost text-warning tooltip"
                      data-tip="Gabungkan Terapis"
                      onClick={() => handleOpenMergeModal(therapist)}
                      aria-label="Gabungkan Terapis"
                    >
                        <FiGitMerge />
                    </button>
                  </PermissionGuard>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </motion.div>

      {/* Tampilan kartu untuk layar kecil */}
      <motion.div variants={fadeInUp} className="md:hidden bg-base-100 shadow border border-base-300 rounded-lg">
        {Array.isArray(filteredTherapists) && filteredTherapists.length === 0 && (
          <div className="text-center text-base-content/70 py-10">
            <FiInbox className="w-10 h-10 mx-auto mb-2 text-base-content/30"/>
            {searchTerm ? `Tidak ada terapis cocok dengan "${searchTerm}".` : 'Belum ada data terapis.'}
          </div>
        )}
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 p-3">
          {Array.isArray(filteredTherapists) && filteredTherapists.map((therapist) => (
            <div key={therapist.id} className="card bg-base-100 shadow-sm border border-base-300/50">
              <div className="card-body p-4">
                <div className="flex justify-between items-start mb-2">
                  <h3 className="card-title text-base font-medium text-base-content">{therapist.name}</h3>
                  {therapist.isActive ? (
                    <span className="badge badge-success badge-sm text-success-content"><FiCheck size={12} className="mr-1"/> Aktif</span>
                  ) : (
                    <span className="badge badge-error badge-sm text-error-content"><FiX size={12} className="mr-1"/> Nonaktif</span>
                  )}
                </div>

                <div className="text-sm text-base-content/80 mb-3">
                  <div className="flex items-center gap-1 mb-1">
                    <FiPhone size={14} className="text-base-content/60" />
                    <span>{therapist.phone || '-'}</span>
                  </div>
                  <div className="flex items-center gap-1 mb-1">
                    <FiMapPin size={14} className="text-base-content/60" />
                    <span className="text-xs text-base-content/60">{therapist.outlet?.name || therapist.outletId}</span>
                  </div>
                  {therapist.captain && (
                    <div className="flex items-center gap-1 mt-1">
                      <FiUsers size={14} className="text-secondary" />
                      <span className="text-secondary font-medium text-xs">Kapten: {therapist.captain.name}</span>
                    </div>
                  )}
                </div>

                <div className="card-actions justify-end border-t border-base-200 pt-3 flex-wrap">
                  {/* Tombol Riwayat & Detail hanya untuk non-STAFF */}
                  <PermissionGuard
                    module="therapists"
                    action="read"
                    fallback={null}
                  >
                    {user?.role !== 'STAFF' && (
                      <button
                        type="button"
                        className="btn btn-xs btn-ghost text-accent"
                        onClick={() => handleOpenHistoryModal(therapist)}
                        aria-label="Lihat Riwayat"
                      >
                        <FiList className="mr-1" /> Riwayat
                      </button>
                    )}
                  </PermissionGuard>
                  <PermissionGuard module="therapists" action="update">
                    <button
                      type="button"
                      className="btn btn-xs btn-ghost text-info"
                      onClick={() => handleOpenAddEditModal(therapist)}
                      aria-label="Edit Terapis"
                    >
                      <FiEdit className="mr-1" /> Edit
                    </button>
                  </PermissionGuard>
                  <PermissionGuard module="therapists" action="delete">
                    <button
                      type="button"
                      className="btn btn-xs btn-ghost text-error"
                      onClick={() => handleDeleteTherapist(therapist)}
                      aria-label="Hapus Terapis"
                    >
                      <FiTrash2 className="mr-1" /> Hapus
                    </button>
                  </PermissionGuard>
                  <PermissionGuard module="therapists" action="update">
                    <button
                      type="button"
                      className="btn btn-xs btn-ghost text-warning"
                      onClick={() => handleOpenMergeModal(therapist)}
                      aria-label="Gabungkan Terapis"
                    >
                      <FiGitMerge className="mr-1" /> Gabung
                    </button>
                  </PermissionGuard>
                </div>
              </div>
            </div>
          ))}
        </div>
      </motion.div>

      {/* --- Modal Tambah/Edit Terapis --- */}
      <dialog id="therapist_add_edit_modal" className={`modal ${isAddEditModalOpen ? 'modal-open' : ''}`}>
         <div className="modal-box w-11/12 max-w-md border border-base-300">
            <button type="button" onClick={handleCloseAddEditModal} className="btn btn-sm btn-circle btn-ghost absolute right-2 top-2 z-10 text-base-content">✕</button>
            <h3 className="font-bold text-lg mb-4 text-base-content">{selectedTherapistForEdit ? 'Edit Terapis' : 'Tambah Terapis Baru'}</h3>
            <div className="space-y-4 py-4">
               <div className="form-control">
                 <label className="label"><span className="label-text text-base-content/80">Nama Terapis<span className="text-error">*</span></span></label>
                 <input type="text" name="name" placeholder="Nama Lengkap" value={formDataTherapist.name} onChange={handleInputChangeTherapist} className="input input-bordered w-full text-base-content" />
               </div>
               <div className="form-control">
                 <label className="label"><span className="label-text text-base-content/80"><FiPhone className="inline-block mr-1"/> Nomor Telepon</span></label>
                 <input type="tel" name="phone" placeholder="08xxxx (opsional)" value={formDataTherapist.phone ?? ''} onChange={handleInputChangeTherapist} className="input input-bordered w-full text-base-content" />
               </div>
               <div className="form-control">
                  <label className="label"><span className="label-text text-base-content/80"><FiMapPin className="inline-block mr-1"/> Lokasi Outlet<span className="text-error">*</span></span></label>
                  <select
                    name="outletId"
                    className="select select-bordered w-full text-base-content"
                    value={formDataTherapist.outletId}
                    onChange={handleInputChangeTherapist}
                    disabled={isLoadingOutlets}
                  >
                    <option value="" disabled>-- Pilih Outlet --</option>
                    {outlets.map(outlet => (
                      <option key={outlet.id} value={outlet.id}>{outlet.name}</option>
                    ))}
                  </select>
               </div>
               <div className="form-control">
                  <label className="label cursor-pointer justify-start gap-4"><span className="label-text text-base-content/80">Status Aktif</span><input type="checkbox" name="isActive" checked={formDataTherapist.isActive} onChange={handleInputChangeTherapist} className="toggle toggle-success toggle-sm" /></label>
               </div>

               {/* Dropdown Kapten */}
               <div className="form-control">
                  <label className="label">
                     <span className="label-text text-base-content/80">Kapten Terapis</span>
                  </label>
                  <select
                     name="captainUserId"
                     value={formDataTherapist.captainUserId || ''}
                     onChange={handleInputChangeTherapist}
                     className="select select-bordered w-full text-base-content"
                     aria-label="Pilih Kapten"
                  >
                     <option value="">-- Tidak Ada Kapten --</option>
                     {captains.map(captain => (
                        <option key={captain.id} value={captain.id}>
                           {captain.name}
                        </option>
                     ))}
                  </select>
                  <label className="label">
                     <span className="label-text-alt text-base-content/60">Pilih kapten yang akan memimpin terapis ini</span>
                  </label>
               </div>
            </div>
            <div className="modal-action"><button type="button" className="btn btn-sm btn-ghost" onClick={handleCloseAddEditModal}>Batal</button><button type="button" className="btn btn-primary btn-sm" onClick={handleSaveTherapist}>Simpan</button></div>
         </div>
         <form method="dialog" className="modal-backdrop"> <button type="button" onClick={handleCloseAddEditModal}>close</button> </form>
      </dialog>

      {/* --- Modal Detail Riwayat & Komisi --- */}
      <dialog id="therapist_history_modal" className={`modal ${isHistoryModalOpen ? 'modal-open' : ''}`}>
         <div className="modal-box w-11/12 max-w-3xl border border-base-300">
            <button type="button" onClick={handleCloseHistoryModal} className="btn btn-sm btn-circle btn-ghost absolute right-2 top-2 z-10 text-base-content">✕</button>
            {selectedTherapistForHistory && (
               <>
                  <h3 className="font-bold text-xl mb-1 text-base-content">{selectedTherapistForHistory.name}</h3>
                  <p className="text-sm text-base-content/70 mb-2">{selectedTherapistForHistory.phone} - {selectedTherapistForHistory.outlet?.name}</p>

                  {/* Filter Tanggal */}
                  <div className="flex flex-col sm:flex-row gap-2 mb-4 bg-base-200/30 p-3 rounded-md border border-base-300">
                    <div className="form-control w-full sm:w-1/2">
                      <label className="label py-0">
                        <span className="label-text text-xs flex items-center gap-1">
                          <FiCalendar size={12} className="text-primary" /> Tanggal Mulai
                        </span>
                      </label>
                      <div className="dropdown dropdown-bottom w-full">
                        <div tabIndex={0} role="button" className="btn btn-xs btn-outline w-full justify-between overflow-hidden h-8">
                          <span className="truncate">{formatDateDisplay(historyStartDate)}</span>
                          <span className="text-xs">▼</span>
                        </div>
                        <div tabIndex={0} className="dropdown-content z-[1] card card-compact shadow bg-base-100 rounded-box w-72">
                          <div className="card-body p-2">
                            <div className="w-full mx-auto">
                              <div className="flex justify-between items-center py-1 mb-1">
                                <button type="button" className="btn btn-xs btn-ghost" onClick={() => setHistoryStartDate(prev => {
                                  const newDate = new Date(prev);
                                  newDate.setMonth(newDate.getMonth() - 1);
                                  return newDate;
                                })}>
                                  «
                                </button>
                                <div className="text-xs font-medium">
                                  {format(historyStartDate, 'MMMM yyyy', { locale: idLocale })}
                                </div>
                                <button type="button" className="btn btn-xs btn-ghost" onClick={() => setHistoryStartDate(prev => {
                                  const newDate = new Date(prev);
                                  newDate.setMonth(newDate.getMonth() + 1);
                                  return newDate;
                                })}>
                                  »
                                </button>
                              </div>
                              <div className="grid grid-cols-7 gap-1">
                                {/* Hari dalam seminggu */}
                                {['Min', 'Sen', 'Sel', 'Rab', 'Kam', 'Jum', 'Sab'].map(day => (
                                  <div key={day} className="text-center text-xs font-medium text-gray-500 py-1">{day}</div>
                                ))}

                                {/* Tanggal */}
                                {Array.from({ length: 42 }, (_, i) => {
                                  const firstDayOfMonth = new Date(historyStartDate.getFullYear(), historyStartDate.getMonth(), 1);
                                  const startingDayOfWeek = firstDayOfMonth.getDay();
                                  const day = i - startingDayOfWeek + 1;
                                  const date = new Date(historyStartDate.getFullYear(), historyStartDate.getMonth(), day);
                                  const isCurrentMonth = date.getMonth() === historyStartDate.getMonth();
                                  const isSelected = date.getDate() === historyStartDate.getDate() &&
                                    date.getMonth() === historyStartDate.getMonth() && date.getFullYear() === historyStartDate.getFullYear();
                                  const isToday = new Date().toDateString() === date.toDateString();

                                  return (
                                    <button
                                      type="button"
                                      key={i}
                                      className={`text-center py-1 text-xs rounded-md w-full h-6 ${
                                        isCurrentMonth ? 'text-gray-700' : 'text-gray-400'
                                      } ${isSelected ? 'bg-primary text-white' : ''} ${
                                        isToday && !isSelected ? 'border border-primary' : ''
                                      } hover:bg-base-200 ${isCurrentMonth ? '' : 'opacity-50'}`}
                                      onClick={() => {
                                        const newDate = new Date(date);
                                        handleHistoryStartDateChange(newDate);
                                      }}
                                      disabled={!isCurrentMonth}
                                    >
                                      {date.getDate()}
                                    </button>
                                  );
                                })}
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>

                    <div className="form-control w-full sm:w-1/2">
                      <label className="label py-0">
                        <span className="label-text text-xs flex items-center gap-1">
                          <FiCalendar size={12} className="text-primary" /> Tanggal Akhir
                        </span>
                      </label>
                      <div className="dropdown dropdown-bottom w-full">
                        <div tabIndex={0} role="button" className="btn btn-xs btn-outline w-full justify-between overflow-hidden h-8">
                          <span className="truncate">{formatDateDisplay(historyEndDate)}</span>
                          <span className="text-xs">▼</span>
                        </div>
                        <div tabIndex={0} className="dropdown-content z-[1] card card-compact shadow bg-base-100 rounded-box w-72">
                          <div className="card-body p-2">
                            <div className="w-full mx-auto">
                              <div className="flex justify-between items-center py-1 mb-1">
                                <button type="button" className="btn btn-xs btn-ghost" onClick={() => setHistoryEndDate(prev => {
                                  const newDate = new Date(prev);
                                  newDate.setMonth(newDate.getMonth() - 1);
                                  return newDate;
                                })}>
                                  «
                                </button>
                                <div className="text-xs font-medium">
                                  {format(historyEndDate, 'MMMM yyyy', { locale: idLocale })}
                                </div>
                                <button type="button" className="btn btn-xs btn-ghost" onClick={() => setHistoryEndDate(prev => {
                                  const newDate = new Date(prev);
                                  newDate.setMonth(newDate.getMonth() + 1);
                                  return newDate;
                                })}>
                                  »
                                </button>
                              </div>
                              <div className="grid grid-cols-7 gap-1">
                                {/* Hari dalam seminggu */}
                                {['Min', 'Sen', 'Sel', 'Rab', 'Kam', 'Jum', 'Sab'].map(day => (
                                  <div key={day} className="text-center text-xs font-medium text-gray-500 py-1">{day}</div>
                                ))}

                                {/* Tanggal */}
                                {Array.from({ length: 42 }, (_, i) => {
                                  const firstDayOfMonth = new Date(historyEndDate.getFullYear(), historyEndDate.getMonth(), 1);
                                  const startingDayOfWeek = firstDayOfMonth.getDay();
                                  const day = i - startingDayOfWeek + 1;
                                  const date = new Date(historyEndDate.getFullYear(), historyEndDate.getMonth(), day);
                                  const isCurrentMonth = date.getMonth() === historyEndDate.getMonth();
                                  const isSelected = date.getDate() === historyEndDate.getDate() &&
                                    date.getMonth() === historyEndDate.getMonth() && date.getFullYear() === historyEndDate.getFullYear();
                                  const isToday = new Date().toDateString() === date.toDateString();
                                  const isBeforeStartDate = date < historyStartDate;

                                  return (
                                    <button
                                      type="button"
                                      key={i}
                                      className={`text-center py-1 text-xs rounded-md w-full h-6 ${
                                        isCurrentMonth ? 'text-gray-700' : 'text-gray-400'
                                      } ${isSelected ? 'bg-primary text-white' : ''} ${
                                        isToday && !isSelected ? 'border border-primary' : ''
                                      } hover:bg-base-200 ${isCurrentMonth ? '' : 'opacity-50'}`}
                                      onClick={() => {
                                        const newDate = new Date(date);
                                        handleHistoryEndDateChange(newDate);
                                      }}
                                      disabled={!isCurrentMonth || isBeforeStartDate}
                                    >
                                      {date.getDate()}
                                    </button>
                                  );
                                })}
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>

                    <div className="form-control w-full sm:w-auto flex-none">
                      <label className="label py-0">
                        <span className="label-text text-xs">Filter</span>
                      </label>
                      <button
                        type="button"
                        className="btn btn-xs btn-primary h-8"
                        onClick={() => filterTransactionsByDate(allTherapistTransactions)}
                      >
                        <FiFilter size={12} /> Terapkan
                      </button>
                    </div>
                  </div>

                  {/* Card untuk Total Penjualan dan Komisi */}
                  {!isHistoryLoading && Array.isArray(therapistTransactionHistory) && therapistTransactionHistory.length > 0 && (
                    (() => {
                      // Hitung total penjualan terapis sesuai aturan bisnis:
                      // Penjualan = harga asli layanan (sebelum diskon) + biaya tambahan
                      const totalSales = therapistTransactionHistory.reduce((sum, trx) => {
                        return sum + getTherapistSalesTotal(trx);
                      }, 0);

                      // Hitung total komisi (pastikan tidak termasuk biaya tambahan)
                      const totalCommissionDisplay = therapistTransactionHistory.reduce((sum, trx) => {
                        // Gunakan nilai komisi yang sudah disimpan di database
                        // Biaya tambahan tidak boleh mempengaruhi nilai komisi
                        return sum + (trx.therapistCommissionEarned ?? 0);
                      }, 0);

                      const commissionPercentage = totalSales > 0 ? ((totalCommissionDisplay / totalSales) * 100).toFixed(1) : '0.0';

                      return (
                        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-4">
                          <div className="card bg-base-100 shadow-sm border border-base-300">
                            <div className="card-body p-4">
                              <h5 className="card-title text-base font-medium text-base-content mb-1">Total Penjualan</h5>
                              <p className="text-2xl font-bold text-primary">
                                Rp {totalSales.toLocaleString('id-ID')}
                              </p>
                              <p className="text-xs text-base-content/70 mt-1">{therapistTransactionHistory.length} transaksi</p>
                              <p className="text-xs text-base-content/60 mt-1">*Harga asli + biaya tambahan</p>
                            </div>
                          </div>
                          <div className="card bg-base-100 shadow-sm border border-base-300">
                            <div className="card-body p-4">
                              <h5 className="card-title text-base font-medium text-base-content mb-1">Total Komisi</h5>
                              <p className="text-2xl font-bold text-secondary">
                                Rp {totalCommissionDisplay.toLocaleString('id-ID')}
                              </p>
                              <p className="text-xs text-base-content/70 mt-1">
                                {commissionPercentage}% dari penjualan
                              </p>
                            </div>
                          </div>
                        </div>
                      );
                    })()
                  )}

                  <h4 className="font-semibold text-base-content mb-2 flex items-center"><FiList className="mr-2 text-accent"/> Riwayat Transaksi & Komisi</h4>
                  {isHistoryLoading ? ( <div className="flex justify-center items-center h-40"><span className="loading loading-spinner text-accent"></span></div> ) : (
                     <div className="max-h-96 overflow-y-auto bg-base-200/30 p-3 rounded-md border border-base-300">
                        <table className="table table-compact w-full text-xs">
                           <thead className="text-base-content/80"><tr><th>Tgl & ID</th><th>Layanan</th><th>Total Trx</th><th>Komisi (Rp)</th><th>Aksi</th></tr></thead>
                           <tbody>
                              {Array.isArray(therapistTransactionHistory) && therapistTransactionHistory.length === 0 && (<tr><td colSpan={5} className="text-center text-base-content/70 py-6 italic">Tidak ada riwayat transaksi.</td></tr>)}
                              {Array.isArray(therapistTransactionHistory) && therapistTransactionHistory
                                .filter(trx => trx)
                                .map(trx => (
                                  <tr key={trx.transactionId} className="hover">
                                    <td>
                                      <div className="font-mono text-[0.7rem] text-base-content/80">{trx.displayId || trx.transactionId || trx.id}</div>
                                      <div className="text-base-content/70">
                                        {getTransactionDate(trx)}
                                      </div>
                                    </td>
                                    <td className="text-base-content/90">{getServiceNamesFromTransaction(trx)}</td>
                                    <td className="font-medium text-base-content">
                                      {/* Hitung total transaksi termasuk biaya tambahan */}
                                      <div className="flex flex-col">
                                        {/* Tampilkan total (termasuk biaya tambahan) menggunakan helper function */}
                                        <div className="font-medium">
                                          Rp {getTherapistSalesTotal(trx).toLocaleString('id-ID')}
                                        </div>

                                        {/* Tampilkan detail jika ada biaya tambahan */}
                                        {trx.additionalCharge && trx.additionalCharge > 0 && (
                                          <div className="text-xs text-base-content/70 mt-1">
                                            <div>Layanan: Rp {getOriginalServiceTotal(trx).toLocaleString('id-ID')}</div>
                                            <div className="text-info">+Biaya: Rp {trx.additionalCharge.toLocaleString('id-ID')}</div>
                                          </div>
                                        )}
                                      </div>
                                    </td>
                                    <td className="font-medium text-secondary">Rp {(trx.therapistCommissionEarned ?? 0).toLocaleString('id-ID')}</td>
                                    <td>
                                      <PermissionGuard module="therapists" action="update">
                                        <button type="button" className="btn btn-xs btn-ghost text-info" onClick={() => handleOpenCommissionEditModal(trx)}><FiEdit size={12}/></button>
                                      </PermissionGuard>
                                    </td>
                                 </tr>
                              ))}
                           </tbody>
                        </table>
                     </div>
                  )}
                  <div className="modal-action mt-6"><button type="button" className="btn btn-sm" onClick={handleCloseHistoryModal}>Tutup</button></div>
               </>
            )}
         </div>
         <form method="dialog" className="modal-backdrop"> <button type="button" onClick={handleCloseHistoryModal}>close</button> </form>
      </dialog>

       {/* --- Modal Edit Komisi (Kecil) --- */}
       <dialog id="edit_commission_modal" className={`modal ${isCommissionEditModalOpen ? 'modal-open' : ''}`}>
          <div className="modal-box max-w-xs border border-base-300">
             <button type="button" onClick={handleCloseCommissionEditModal} className="btn btn-sm btn-circle btn-ghost absolute right-2 top-2 z-10 text-base-content">✕</button>
             <h3 className="font-bold text-base text-base-content mb-3">Edit Komisi Terapis</h3>
             {selectedTrxForCommissionEdit && (
                <div className="text-xs text-base-content/70 mb-3">
                  <p>Trx ID: {selectedTrxForCommissionEdit.displayId || selectedTrxForCommissionEdit.transactionId}</p>
                  <p className="mt-1">
                    Tanggal: {getTransactionDate(selectedTrxForCommissionEdit)}
                  </p>
                  <p className="mt-1">Pelanggan: {getCustomerName(selectedTrxForCommissionEdit)}</p>

                  {/* Tampilkan detail transaksi dengan biaya tambahan */}
                  <div className="mt-2 pt-2 border-t border-base-200">
                    <p className="flex justify-between">
                      <span>Harga Layanan:</span>
                      <span>
                        Rp {getOriginalServiceTotal(selectedTrxForCommissionEdit).toLocaleString('id-ID')}
                      </span>
                    </p>

                    {selectedTrxForCommissionEdit.additionalCharge && selectedTrxForCommissionEdit.additionalCharge > 0 && (
                      <p className="flex justify-between text-info">
                        <span>Biaya Tambahan:</span>
                        <span>+Rp {selectedTrxForCommissionEdit.additionalCharge.toLocaleString('id-ID')}</span>
                      </p>
                    )}

                    {selectedTrxForCommissionEdit.additionalCharge && selectedTrxForCommissionEdit.additionalCharge > 0 && (
                      <p className="flex justify-between font-medium mt-1 border-t border-base-200 pt-1">
                        <span>Total Penjualan:</span>
                        <span>
                          Rp {getTherapistSalesTotal(selectedTrxForCommissionEdit).toLocaleString('id-ID')}
                        </span>
                      </p>
                    )}
                  </div>
                </div>
             )}
              <div className="form-control">
                <label className="label"><span className="label-text text-base-content/80">Komisi Diterima (Rp)</span></label>
                <input
                  type="number"
                  className="input input-bordered w-full text-base-content [appearance:textfield] [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none"
                  value={editedCommissionValue}
                  onChange={(e) => setEditedCommissionValue(parseInt(e.target.value) || 0)}
                  min="0"
                  step="1000"
                 />
              </div>
             <div className="modal-action mt-4"><button type="button" className="btn btn-sm" onClick={handleCloseCommissionEditModal}>Batal</button><button type="button" className="btn btn-primary btn-sm" onClick={handleSaveCommission}>Simpan Komisi</button></div>
          </div>
          <form method="dialog" className="modal-backdrop"> <button type="button" onClick={handleCloseCommissionEditModal}>close</button> </form>
       </dialog>

       {/* --> Modal Konfirmasi Hapus Terapis (BARU) <-- */}
       <dialog id="delete_therapist_modal" className={`modal ${isDeleteTherapistModalOpen ? 'modal-open' : ''}`}>
         <div className="modal-box max-w-sm border border-base-300">
           <button type="button" onClick={handleCloseDeleteTherapistModal} className="btn btn-sm btn-circle btn-ghost absolute right-2 top-2 z-10">✕</button>
           <h3 className="font-bold text-lg text-error">Konfirmasi Hapus Terapis</h3>
           <p className="py-4 text-base-content/80">
             Anda yakin ingin menghapus terapis &quot;<span className="font-semibold">{therapistToDelete?.name}</span>&quot;?
             <br/> Tindakan ini tidak dapat dibatalkan dan akan menghapus data terapis secara permanen jika tidak ada riwayat booking/transaksi.
           </p>
           <div className="modal-action">
             <button type="button" className="btn btn-sm btn-ghost" onClick={handleCloseDeleteTherapistModal} disabled={isLoading}>
               Batal
             </button>
             <button type="button" className="btn btn-sm btn-error" onClick={confirmDeleteTherapist} disabled={isLoading}>
               {isLoading && <span className="loading loading-spinner loading-xs"></span>}
               Ya, Hapus
             </button>
           </div>
         </div>
         <form method="dialog" className="modal-backdrop"> <button type="button" onClick={handleCloseDeleteTherapistModal}>close</button> </form>
       </dialog>

       {/* --> Modal Penggabungan Terapis <-- */}
       <dialog id="merge_therapist_modal" className={`modal ${isMergeModalOpen ? 'modal-open' : ''}`}>
         <div className="modal-box w-11/12 max-w-2xl border border-base-300">
           <button type="button" onClick={handleCloseMergeModal} className="btn btn-sm btn-circle btn-ghost absolute right-2 top-2 z-10">✕</button>
           <h3 className="font-bold text-lg text-warning flex items-center">
             <FiGitMerge className="mr-2"/> Gabungkan Terapis
           </h3>

           {primaryTherapist && (
             <div className="mt-4">
               <div className="bg-base-200/30 p-3 rounded-md border border-base-300 mb-4">
                 <h4 className="font-medium text-base-content mb-2">Terapis Utama (Tujuan Penggabungan)</h4>
                 <div className="flex flex-col md:flex-row gap-2 items-start md:items-center">
                   <div className="badge badge-lg badge-primary text-primary-content">{primaryTherapist.name}</div>
                   <div className="text-xs text-base-content/70">
                     {primaryTherapist.phone && <span className="mr-2">{primaryTherapist.phone}</span>}
                     {primaryTherapist.outlet?.name && <span>{primaryTherapist.outlet.name}</span>}
                   </div>
                 </div>
                 <p className="text-xs text-base-content/70 mt-2">
                   Semua riwayat transaksi, booking, dan data lainnya akan digabungkan ke terapis ini.
                 </p>
               </div>

               <div className="mb-4">
                 <h4 className="font-medium text-base-content mb-2">Pilih Terapis yang Akan Digabungkan</h4>

                 {!secondaryTherapist ? (
                   <div className="form-control">
                     <div className="join w-full">
                       <input
                         type="text"
                         placeholder="Cari terapis yang akan digabungkan..."
                         className="input input-bordered input-sm join-item w-full placeholder:text-base-content/60 text-base-content"
                         value={secondaryTherapistSearchTerm}
                         onChange={(e) => setSecondaryTherapistSearchTerm(e.target.value)}
                       />
                       <button
                         type="button"
                         className="btn btn-ghost btn-sm join-item"
                         aria-label="Cari"
                         title="Cari"
                       >
                         <FiSearch className="text-base-content/70"/>
                       </button>
                     </div>

                     <div className="mt-2 max-h-40 overflow-y-auto bg-base-200/30 p-2 rounded-md border border-base-300">
                       {therapists
                         .filter(t =>
                           t.id !== primaryTherapist.id &&
                           t.isActive &&
                           (
                             !secondaryTherapistSearchTerm ||
                             t.name.toLowerCase().includes(secondaryTherapistSearchTerm.toLowerCase())
                           )
                         )
                         .map(therapist => (
                           <div
                             key={therapist.id}
                             className="p-2 hover:bg-base-300/50 rounded-md cursor-pointer flex justify-between items-center"
                             onClick={() => handleSelectSecondaryTherapist(therapist)}
                           >
                             <div>
                               <div className="font-medium text-base-content">{therapist.name}</div>
                               <div className="text-xs text-base-content/70">
                                 {therapist.phone && <span className="mr-2">{therapist.phone}</span>}
                                 {therapist.outlet?.name && <span>{therapist.outlet.name}</span>}
                               </div>
                             </div>
                             <button
                               type="button"
                               className="btn btn-xs btn-ghost text-warning"
                               onClick={(e) => {
                                 e.stopPropagation();
                                 handleSelectSecondaryTherapist(therapist);
                               }}
                             >
                               Pilih
                             </button>
                           </div>
                         ))
                       }

                       {therapists.filter(t =>
                         t.id !== primaryTherapist.id &&
                         t.isActive &&
                         (
                           !secondaryTherapistSearchTerm ||
                           t.name.toLowerCase().includes(secondaryTherapistSearchTerm.toLowerCase())
                         )
                       ).length === 0 && (
                         <div className="text-center text-base-content/70 py-4">
                           {secondaryTherapistSearchTerm ?
                             `Tidak ada terapis yang cocok dengan "${secondaryTherapistSearchTerm}".` :
                             'Tidak ada terapis lain yang tersedia untuk digabungkan.'}
                         </div>
                       )}
                     </div>
                   </div>
                 ) : (
                   <div className="bg-base-200/30 p-3 rounded-md border border-base-300">
                     <div className="flex flex-col md:flex-row gap-2 items-start md:items-center justify-between">
                       <div>
                         <div className="badge badge-lg badge-warning text-warning-content">{secondaryTherapist.name}</div>
                         <div className="text-xs text-base-content/70 mt-1">
                           {secondaryTherapist.phone && <span className="mr-2">{secondaryTherapist.phone}</span>}
                           {secondaryTherapist.outlet?.name && <span>{secondaryTherapist.outlet.name}</span>}
                         </div>
                       </div>
                       <button
                         type="button"
                         className="btn btn-xs btn-ghost text-base-content"
                         onClick={() => setSecondaryTherapist(null)}
                       >
                         Ganti
                       </button>
                     </div>
                   </div>
                 )}
               </div>

               {secondaryTherapist && (
                 <div className="bg-base-100 p-4 rounded-md border border-warning mb-4">
                   <div className="flex items-center gap-2 mb-2">
                     <FiAlertTriangle className="text-warning" />
                     <h4 className="font-medium text-base-content">Konfirmasi Penggabungan</h4>
                   </div>

                   {mergeStats ? (
                     <div className="text-sm text-base-content/80">
                       <p className="mb-2">Data yang berhasil digabungkan:</p>
                       <ul className="list-disc list-inside text-xs space-y-1">
                         <li>{mergeStats.transactionsCount} transaksi</li>
                         <li>{mergeStats.bookingsCount} booking</li>
                         <li>{mergeStats.commissionsCount} komisi khusus</li>
                         <li>{mergeStats.attendancesCount} data absensi</li>
                       </ul>
                       <p className="mt-2 text-success font-medium">Penggabungan berhasil! Terapis &quot;{secondaryTherapist.name}&quot; telah dinonaktifkan.</p>
                     </div>
                   ) : (
                     <div className="text-sm text-base-content/80">
                       <p>Anda akan menggabungkan terapis <span className="font-medium text-warning">{secondaryTherapist.name}</span> ke terapis <span className="font-medium text-primary">{primaryTherapist.name}</span>.</p>
                       <p className="mt-2">Semua riwayat transaksi, booking, dan data lainnya akan dipindahkan. Terapis yang digabungkan akan dinonaktifkan.</p>
                       <p className="mt-2 text-warning font-medium">Tindakan ini tidak dapat dibatalkan!</p>
                     </div>
                   )}
                 </div>
               )}
             </div>
           )}

           <div className="modal-action">
             <button type="button" className="btn btn-sm btn-ghost" onClick={handleCloseMergeModal} disabled={isMerging}>
               {mergeStats ? 'Tutup' : 'Batal'}
             </button>
             {secondaryTherapist && !mergeStats && (
               <button
                 type="button"
                 className="btn btn-sm btn-warning"
                 onClick={handleConfirmMerge}
                 disabled={isMerging || !secondaryTherapist}
               >
                 {isMerging && <span className="loading loading-spinner loading-xs"></span>}
                 Gabungkan Terapis
               </button>
             )}
           </div>
         </div>
         <form method="dialog" className="modal-backdrop"> <button type="button" onClick={handleCloseMergeModal}>close</button> </form>
       </dialog>

    </motion.div>
  );
}