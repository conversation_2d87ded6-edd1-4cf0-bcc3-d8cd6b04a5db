import { NextResponse } from 'next/server';
import * as jwt from 'jsonwebtoken';
import { prisma } from '@/lib/prisma';

// Gunakan variabel environment dan pastikan ada nilai default yang aman
// Pastikan nilai ini sama di seluruh file autentikasi
const JWT_SECRET = process.env.JWT_SECRET || 'breaktime_session_secret_key_for_jwt_auth';

// Handler OPTIONS untuk CORS pre-flight requests
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Methods': 'GET, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Max-Age': '86400',
    },
  });
}

export async function GET(request: Request) {
  let response;
  try {
    // Log untuk debugging
    console.log('Auth/me request received');

    // Ambil token dari Authorization header
    const authHeader = request.headers.get('authorization');
    let token = authHeader?.startsWith('Bearer ') ? authHeader.substring(7) : null;

    // Jika tidak ada token di header, cek cookie (implementasi sederhana)
    if (!token) {
      const cookieHeader = request.headers.get('cookie');
      if (cookieHeader) {
        const cookies = cookieHeader.split(';').map(c => c.trim());
        const userTokenCookie = cookies.find(c => c.startsWith('user_token='));
        if (userTokenCookie) {
          token = userTokenCookie.split('=')[1];
        }
      }
    }

    console.log('Token found:', !!token);

    // Jika tidak ada token sama sekali
    if (!token) {
      response = NextResponse.json(
        { error: 'Tidak terautentikasi' },
        { status: 401 }
      );
      return addCorsHeaders(response);
    }

    // Verifikasi token
    const decoded = jwt.verify(token, JWT_SECRET) as {
      id: string;
      name: string;
      email: string;
      role: string;
    };

    if (!decoded || !decoded.id) {
      response = NextResponse.json(
        { error: 'Token tidak valid' },
        { status: 401 }
      );
      return addCorsHeaders(response);
    }

    console.log('Token verified for user ID:', decoded.id);

    // Ambil data user dari database
    const user = await prisma.user.findUnique({
      where: { id: decoded.id },
      select: {
        id: true,
        name: true,
        email: true,
        role: true,
        isActive: true,
        lastLoginAt: true
      }
    });

    // Jika user tidak ditemukan atau tidak aktif
    if (!user || !user.isActive) {
      response = NextResponse.json(
        { error: 'User tidak ditemukan atau tidak aktif' },
        { status: 404 }
      );
      return addCorsHeaders(response);
    }

    // Respons sukses dengan header CORS
    response = NextResponse.json({
      message: 'Data user berhasil diambil',
      user
    });

    return addCorsHeaders(response);
  } catch (error) {
    console.error('Auth error:', error);

    // Jika token tidak valid
    if (error instanceof jwt.JsonWebTokenError) {
      response = NextResponse.json(
        { error: 'Token tidak valid' },
        { status: 401 }
      );
      return addCorsHeaders(response);
    }

    // Respons dengan status 500 dan header CORS jika di production
    response = NextResponse.json(
      { error: 'Terjadi kesalahan saat memverifikasi autentikasi' },
      { status: 500 }
    );

    return addCorsHeaders(response);
  }
}

// Helper untuk menambahkan header CORS
function addCorsHeaders(response: NextResponse) {
  // Set headers untuk CORS
  response.headers.set('Access-Control-Allow-Origin', '*');
  response.headers.set('Access-Control-Allow-Methods', 'GET, OPTIONS');
  response.headers.set('Access-Control-Allow-Headers', 'Content-Type, Authorization');
  response.headers.set('Access-Control-Allow-Credentials', 'true');
  return response;
}