generator client {
  provider      = "prisma-client-js"
  output        = "../prisma/generated/client"
  binaryTargets = ["native", "debian-openssl-1.1.x"]
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id                         String               @id @default(uuid())
  username                   String               @unique
  name                       String
  email                      String               @unique
  password                   String
  role                       Role                 @default(STAFF)
  createdAt                  DateTime             @default(now())
  updatedAt                  DateTime             @updatedAt
  lastLoginAt                DateTime?
  isActive                   Boolean              @default(true)
  isCaptain                  Boolean              @default(false)
  bookings                   Booking[]            @relation("BookedBy")
  permissions                Permission[]
  systemLogs                 SystemLog[]
  therapistTeam              Therapist[]          @relation("CaptainTeam")
  transactions               Transaction[]        @relation("CreatedBy")
  allowedOutlets             UserOutletAccess[]   @relation("UserAccess")
  attendances                UserAttendance[]     @relation("UserAttendances")
  // Inventory relations
  inventoryItemsCreated      InventoryItem[]      @relation("InventoryItemCreator")
  inventoryMovementsCreated  InventoryMovement[]  @relation("InventoryMovementCreator")
  inventoryAuditsCreated     InventoryAudit[]     @relation("InventoryAuditCreator")
  inventoryAuditsAssigned    InventoryAudit[]     @relation("InventoryAuditAssignee")
  inventoryAuditItemsChecked InventoryAuditItem[] @relation("InventoryAuditItemChecker")
}

model Outlet {
  id                   String                @id @default(uuid())
  name                 String
  address              String
  city                 String?
  phone                String
  operationalHours     String
  isOpen               Boolean               @default(true)
  isMain               Boolean               @default(false)
  createdAt            DateTime              @default(now())
  updatedAt            DateTime              @updatedAt
  bookings             Booking[]
  services             ServiceOutlet[]
  systemLogs           SystemLog[]
  therapists           Therapist[]
  transactions         Transaction[]
  allowedUsers         UserOutletAccess[]    @relation("OutletAccess")
  therapistAttendances TherapistAttendance[] @relation("OutletAttendances")
  // Inventory relations
  inventoryItems       InventoryItem[]
  inventoryAudits      InventoryAudit[]
}

model Therapist {
  id                 String                       @id @default(uuid())
  name               String
  phone              String?
  experience         Int?
  specialization     String?
  isActive           Boolean                      @default(true)
  createdAt          DateTime                     @default(now())
  updatedAt          DateTime                     @updatedAt
  outletId           String
  captainUserId      String?
  activeSessions     ActiveTherapistSession[]
  bookings           Booking[]
  captain            User?                        @relation("CaptainTeam", fields: [captainUserId], references: [id])
  outlet             Outlet                       @relation(fields: [outletId], references: [id])
  serviceCommissions TherapistServiceCommission[]
  transactions       Transaction[]                @relation("TherapistRelation")
  attendances        TherapistAttendance[]        @relation("TherapistAttendances")
  // Inventory relation (removed - not needed for this system)
}

model Service {
  id                   String                       @id @default(uuid())
  name                 String
  description          String?
  duration             Int
  price                Float
  isActive             Boolean                      @default(true)
  createdAt            DateTime                     @default(now())
  updatedAt            DateTime                     @updatedAt
  commission           Float                        @default(0)
  isCombo              Boolean                      @default(false)
  activeSessions       ActiveTherapistSession[]
  bookingServices      BookingService[]
  comboDetails         ComboServiceDetail[]         @relation("ComboPackage")
  includedInCombos     ComboServiceDetail[]         @relation("IncludedInCombo")
  outlets              ServiceOutlet[]
  therapistCommissions TherapistServiceCommission[]
  transactionItems     TransactionItem[]
}

model Customer {
  id               String            @id
  name             String
  phone            String            @unique
  email            String?
  address          String?
  birthdate        DateTime?
  gender           Gender?
  registeredAt     DateTime          @default(now())
  updatedAt        DateTime          @updatedAt
  isActive         Boolean           @default(true)
  totalVisits      Int               @default(0)
  points           Int               @default(0)
  tags             String[]          @default([])
  bookings         Booking[] // Relasi sebagai pelanggan utama
  transactions     Transaction[]
  bookingCustomers BookingCustomer[] // Relasi ke semua booking
}

model Booking {
  id               String            @id @default(uuid())
  bookingDate      DateTime
  bookingTime      String
  status           BookingStatus     @default(PENDING)
  notes            String?
  createdAt        DateTime          @default(now())
  updatedAt        DateTime          @updatedAt
  createdById      String
  customerId       String // Pelanggan utama
  outletId         String
  therapistId      String
  displayId        String?           @unique
  createdBy        User              @relation("BookedBy", fields: [createdById], references: [id])
  customer         Customer          @relation(fields: [customerId], references: [id])
  outlet           Outlet            @relation(fields: [outletId], references: [id])
  therapist        Therapist         @relation(fields: [therapistId], references: [id])
  bookingServices  BookingService[]
  transaction      Transaction?
  bookingCustomers BookingCustomer[] // Relasi ke semua pelanggan
}

model BookingService {
  id        String  @id @default(uuid())
  bookingId String
  serviceId String
  price     Float
  quantity  Int     @default(1)
  booking   Booking @relation(fields: [bookingId], references: [id], onDelete: Cascade)
  service   Service @relation(fields: [serviceId], references: [id])

  @@unique([bookingId, serviceId])
}

model Transaction {
  transactionDate           DateTime                 @default(now())
  displayId                 String?                  @unique
  totalAmount               Float
  paymentMethod             PaymentMethod            @default(CASH)
  paymentStatus             PaymentStatus            @default(PAID)
  notes                     String?
  createdAt                 DateTime                 @default(now())
  updatedAt                 DateTime                 @updatedAt
  bookingId                 String?                  @unique
  createdById               String
  customerId                String
  outletId                  String
  therapistId               String
  therapistCommissionEarned Float?                   @default(0)
  // Tambahkan field untuk diskon dan biaya tambahan
  discountType              String?
  discountValue             Float?                   @default(0)
  discountAmount            Float?                   @default(0)
  additionalCharge          Float?                   @default(0)
  id                        Int                      @id @default(autoincrement())
  activeSessions            ActiveTherapistSession[]
  splitPayment              SplitPayment?
  booking                   Booking?                 @relation(fields: [bookingId], references: [id])
  createdBy                 User                     @relation("CreatedBy", fields: [createdById], references: [id])
  customer                  Customer                 @relation(fields: [customerId], references: [id])
  outlet                    Outlet                   @relation(fields: [outletId], references: [id])
  therapist                 Therapist                @relation("TherapistRelation", fields: [therapistId], references: [id])
  transactionItems          TransactionItem[]

  @@index([customerId])
  @@index([outletId])
  @@index([therapistId])
  @@index([createdById])
  @@index([createdAt])
}

model SplitPayment {
  id            Int           @id @default(autoincrement())
  firstMethod   PaymentMethod
  secondMethod  PaymentMethod
  cashAmount    Float?        @default(0)
  changeAmount  Float?        @default(0)
  createdAt     DateTime      @default(now())
  transactionId Int           @unique
  transaction   Transaction   @relation(fields: [transactionId], references: [id], onDelete: Cascade)
}

model Permission {
  id        String   @id @default(uuid())
  module    String
  canCreate Boolean  @default(false)
  canRead   Boolean  @default(true)
  canUpdate Boolean  @default(false)
  canDelete Boolean  @default(false)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  userId    String
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([userId, module])
}

model UserOutletAccess {
  id         String   @id @default(uuid())
  assignedAt DateTime @default(now())
  userId     String
  outletId   String
  outlet     Outlet   @relation("OutletAccess", fields: [outletId], references: [id], onDelete: Cascade)
  user       User     @relation("UserAccess", fields: [userId], references: [id], onDelete: Cascade)

  @@unique([userId, outletId])
}

model Setting {
  id        String      @id @default(uuid())
  key       String      @unique
  value     String
  category  String
  label     String
  type      SettingType @default(TEXT)
  options   Json?
  isSystem  Boolean     @default(false)
  createdAt DateTime    @default(now())
  updatedAt DateTime    @updatedAt
}

model SystemLog {
  id        String   @id @default(uuid())
  type      String
  message   String
  details   Json?
  outletId  String?
  userId    String?
  createdAt DateTime @default(now())
  outlet    Outlet?  @relation(fields: [outletId], references: [id])
  user      User?    @relation(fields: [userId], references: [id])

  @@index([type])
  @@index([createdAt])
  @@index([outletId])
  @@index([userId])
}

model TransactionItem {
  id            Int         @id @default(autoincrement())
  quantity      Int         @default(1)
  price         Float
  transactionId Int
  serviceId     String?
  name          String?
  createdAt     DateTime    @default(now())
  service       Service?    @relation(fields: [serviceId], references: [id])
  transaction   Transaction @relation(fields: [transactionId], references: [id], onDelete: Cascade)

  @@index([transactionId])
  @@index([serviceId])
}

model ComboServiceDetail {
  id                String   @id @default(cuid())
  quantity          Int      @default(1)
  comboServiceId    String
  includedServiceId String
  createdAt         DateTime @default(now())
  comboService      Service  @relation("ComboPackage", fields: [comboServiceId], references: [id], onDelete: Cascade)
  includedService   Service  @relation("IncludedInCombo", fields: [includedServiceId], references: [id])

  @@unique([comboServiceId, includedServiceId])
  @@index([comboServiceId])
  @@index([includedServiceId])
}

model ServiceOutlet {
  id        String   @id @default(uuid())
  serviceId String
  outletId  String
  createdAt DateTime @default(now())
  outlet    Outlet   @relation(fields: [outletId], references: [id], onDelete: Cascade)
  service   Service  @relation(fields: [serviceId], references: [id], onDelete: Cascade)

  @@unique([serviceId, outletId])
  @@index([serviceId])
  @@index([outletId])
}

model TherapistServiceCommission {
  id          String    @id @default(uuid())
  therapistId String
  serviceId   String
  commission  Float
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
  service     Service   @relation(fields: [serviceId], references: [id], onDelete: Cascade)
  therapist   Therapist @relation(fields: [therapistId], references: [id], onDelete: Cascade)

  @@unique([therapistId, serviceId])
  @@index([therapistId])
  @@index([serviceId])
}

model ActiveTherapistSession {
  id            String      @id @default(uuid())
  therapistId   String
  serviceId     String
  startTime     DateTime    @default(now())
  endTime       DateTime
  transactionId Int
  createdAt     DateTime    @default(now())
  service       Service     @relation(fields: [serviceId], references: [id])
  therapist     Therapist   @relation(fields: [therapistId], references: [id], onDelete: Cascade)
  transaction   Transaction @relation(fields: [transactionId], references: [id], onDelete: Cascade)

  @@index([therapistId])
  @@index([serviceId])
  @@index([transactionId])
  @@index([startTime])
  @@index([endTime])
}

model BookingCustomer {
  id         String   @id @default(uuid())
  bookingId  String
  customerId String
  createdAt  DateTime @default(now())
  booking    Booking  @relation(fields: [bookingId], references: [id], onDelete: Cascade)
  customer   Customer @relation(fields: [customerId], references: [id])

  @@unique([bookingId, customerId])
}

model TherapistAttendance {
  id             String   @id @default(uuid())
  therapistId    String
  outletId       String
  attendanceType String // IN atau OUT
  attendanceTime DateTime @default(now())
  createdAt      DateTime @default(now())
  notes          String?

  therapist Therapist @relation("TherapistAttendances", fields: [therapistId], references: [id])
  outlet    Outlet    @relation("OutletAttendances", fields: [outletId], references: [id])

  @@index([therapistId])
  @@index([outletId])
  @@index([attendanceTime])
}

model UserAttendance {
  id             String   @id @default(uuid())
  userId         String
  attendanceType String // IN atau OUT
  attendanceTime DateTime @default(now())
  createdAt      DateTime @default(now())
  notes          String?

  user User @relation("UserAttendances", fields: [userId], references: [id])

  @@index([userId])
  @@index([attendanceTime])
}

enum Role {
  ADMIN
  MANAGER
  STAFF
  INVESTOR
  CUSTOMER
  ASSISTANT
}

enum Gender {
  MALE
  FEMALE
  OTHER
}

enum BookingStatus {
  PENDING
  CONFIRMED
  COMPLETED
  CANCELLED
  NO_SHOW
}

enum PaymentMethod {
  CASH
  CREDIT_CARD
  DEBIT_CARD
  TRANSFER
  DIGITAL_WALLET
  QRIS
  SPLIT
}

enum PaymentStatus {
  PENDING
  PAID
  REFUNDED
  FAILED
}

enum SettingType {
  TEXT
  NUMBER
  BOOLEAN
  SELECT
  COLOR
  DATE
  TIME
  DATETIME
  IMAGE
  RICHTEXT
}

enum InventoryStatus {
  BAIK
  RUSAK
  HILANG
}

enum InventoryAuditStatus {
  PENDING
  IN_PROGRESS
  COMPLETED
  CANCELLED
}

model InventoryCategory {
  id          String          @id @default(uuid())
  name        String          @unique
  description String?
  isActive    Boolean         @default(true)
  createdAt   DateTime        @default(now())
  updatedAt   DateTime        @updatedAt
  items       InventoryItem[]
}

model InventoryItem {
  id                String               @id @default(uuid())
  name              String
  description       String?
  categoryId        String
  outletId          String
  totalQuantity     Int                  @default(0)
  goodCondition     Int                  @default(0)
  damagedCondition  Int                  @default(0)
  lostCondition     Int                  @default(0)
  remainingQuantity Int                  @default(0)
  minStockLevel     Int?                 @default(0)
  maxStockLevel     Int?                 @default(0)
  unitPrice         Decimal?             @default(0) @db.Decimal(10, 2)
  supplier          String?
  purchaseDate      DateTime?
  warrantyExpiry    DateTime?
  notes             String?
  isActive          Boolean              @default(true)
  createdAt         DateTime             @default(now())
  updatedAt         DateTime             @updatedAt
  createdById       String
  category          InventoryCategory    @relation(fields: [categoryId], references: [id])
  outlet            Outlet               @relation(fields: [outletId], references: [id])
  createdBy         User                 @relation("InventoryItemCreator", fields: [createdById], references: [id])
  movements         InventoryMovement[]
  auditItems        InventoryAuditItem[]

  @@index([categoryId])
  @@index([outletId])
  @@index([createdById])
}

model InventoryMovement {
  id           String           @id @default(uuid())
  itemId       String
  movementType String // IN, OUT, TRANSFER, ADJUST, DAMAGE, LOST
  quantity     Int
  fromStatus   InventoryStatus?
  toStatus     InventoryStatus?
  reason       String?
  notes        String?
  createdAt    DateTime         @default(now())
  createdById  String
  item         InventoryItem    @relation(fields: [itemId], references: [id], onDelete: Cascade)
  createdBy    User             @relation("InventoryMovementCreator", fields: [createdById], references: [id])

  @@index([itemId])
  @@index([createdById])
  @@index([createdAt])
}

model InventoryAudit {
  id            String               @id @default(uuid())
  title         String
  description   String?
  outletId      String
  status        InventoryAuditStatus @default(PENDING)
  scheduledDate DateTime
  startedAt     DateTime?
  completedAt   DateTime?
  createdAt     DateTime             @default(now())
  updatedAt     DateTime             @updatedAt
  createdById   String
  assignedToId  String?
  outlet        Outlet               @relation(fields: [outletId], references: [id])
  createdBy     User                 @relation("InventoryAuditCreator", fields: [createdById], references: [id])
  assignedTo    User?                @relation("InventoryAuditAssignee", fields: [assignedToId], references: [id])
  auditItems    InventoryAuditItem[]

  @@index([outletId])
  @@index([createdById])
  @@index([assignedToId])
}

model InventoryAuditItem {
  id              String         @id @default(uuid())
  auditId         String
  itemId          String
  expectedGood    Int            @default(0)
  expectedDamaged Int            @default(0)
  expectedLost    Int            @default(0)
  actualGood      Int?
  actualDamaged   Int?
  actualLost      Int?
  variance        Int?           @default(0)
  notes           String?
  isChecked       Boolean        @default(false)
  checkedAt       DateTime?
  checkedById     String?
  audit           InventoryAudit @relation(fields: [auditId], references: [id], onDelete: Cascade)
  item            InventoryItem  @relation(fields: [itemId], references: [id], onDelete: Cascade)
  checkedBy       User?          @relation("InventoryAuditItemChecker", fields: [checkedById], references: [id])

  @@unique([auditId, itemId])
  @@index([auditId])
  @@index([itemId])
  @@index([checkedById])
}
