
Object.defineProperty(exports, "__esModule", { value: true });

const {
  Decimal,
  objectEnumValues,
  makeStrictEnum,
  Public,
  getRuntime,
  skip
} = require('./runtime/index-browser.js')


const Prisma = {}

exports.Prisma = Prisma
exports.$Enums = {}

/**
 * Prisma Client JS version: 6.6.0
 * Query Engine version: f676762280b54cd07c770017ed3711ddde35f37a
 */
Prisma.prismaVersion = {
  client: "6.6.0",
  engine: "f676762280b54cd07c770017ed3711ddde35f37a"
}

Prisma.PrismaClientKnownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientKnownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)};
Prisma.PrismaClientUnknownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientUnknownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientRustPanicError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientRustPanicError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientInitializationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientInitializationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientValidationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientValidationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.Decimal = Decimal

/**
 * Re-export of sql-template-tag
 */
Prisma.sql = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`sqltag is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.empty = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`empty is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.join = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`join is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.raw = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`raw is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.validator = Public.validator

/**
* Extensions
*/
Prisma.getExtensionContext = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.getExtensionContext is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.defineExtension = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.defineExtension is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}

/**
 * Shorthand utilities for JSON filtering
 */
Prisma.DbNull = objectEnumValues.instances.DbNull
Prisma.JsonNull = objectEnumValues.instances.JsonNull
Prisma.AnyNull = objectEnumValues.instances.AnyNull

Prisma.NullTypes = {
  DbNull: objectEnumValues.classes.DbNull,
  JsonNull: objectEnumValues.classes.JsonNull,
  AnyNull: objectEnumValues.classes.AnyNull
}



/**
 * Enums
 */

exports.Prisma.TransactionIsolationLevel = makeStrictEnum({
  ReadUncommitted: 'ReadUncommitted',
  ReadCommitted: 'ReadCommitted',
  RepeatableRead: 'RepeatableRead',
  Serializable: 'Serializable'
});

exports.Prisma.UserScalarFieldEnum = {
  id: 'id',
  username: 'username',
  name: 'name',
  email: 'email',
  password: 'password',
  role: 'role',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  lastLoginAt: 'lastLoginAt',
  isActive: 'isActive',
  isCaptain: 'isCaptain'
};

exports.Prisma.OutletScalarFieldEnum = {
  id: 'id',
  name: 'name',
  address: 'address',
  city: 'city',
  phone: 'phone',
  operationalHours: 'operationalHours',
  isOpen: 'isOpen',
  isMain: 'isMain',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.TherapistScalarFieldEnum = {
  id: 'id',
  name: 'name',
  phone: 'phone',
  experience: 'experience',
  specialization: 'specialization',
  isActive: 'isActive',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  outletId: 'outletId',
  captainUserId: 'captainUserId'
};

exports.Prisma.ServiceScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  duration: 'duration',
  price: 'price',
  isActive: 'isActive',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  commission: 'commission',
  isCombo: 'isCombo'
};

exports.Prisma.CustomerScalarFieldEnum = {
  id: 'id',
  name: 'name',
  phone: 'phone',
  email: 'email',
  address: 'address',
  birthdate: 'birthdate',
  gender: 'gender',
  registeredAt: 'registeredAt',
  updatedAt: 'updatedAt',
  isActive: 'isActive',
  totalVisits: 'totalVisits',
  points: 'points',
  tags: 'tags'
};

exports.Prisma.BookingScalarFieldEnum = {
  id: 'id',
  bookingDate: 'bookingDate',
  bookingTime: 'bookingTime',
  status: 'status',
  notes: 'notes',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  createdById: 'createdById',
  customerId: 'customerId',
  outletId: 'outletId',
  therapistId: 'therapistId',
  displayId: 'displayId'
};

exports.Prisma.BookingServiceScalarFieldEnum = {
  id: 'id',
  bookingId: 'bookingId',
  serviceId: 'serviceId',
  price: 'price',
  quantity: 'quantity'
};

exports.Prisma.TransactionScalarFieldEnum = {
  transactionDate: 'transactionDate',
  displayId: 'displayId',
  totalAmount: 'totalAmount',
  paymentMethod: 'paymentMethod',
  paymentStatus: 'paymentStatus',
  notes: 'notes',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  bookingId: 'bookingId',
  createdById: 'createdById',
  customerId: 'customerId',
  outletId: 'outletId',
  therapistId: 'therapistId',
  therapistCommissionEarned: 'therapistCommissionEarned',
  discountType: 'discountType',
  discountValue: 'discountValue',
  discountAmount: 'discountAmount',
  additionalCharge: 'additionalCharge',
  id: 'id'
};

exports.Prisma.SplitPaymentScalarFieldEnum = {
  id: 'id',
  firstMethod: 'firstMethod',
  secondMethod: 'secondMethod',
  cashAmount: 'cashAmount',
  changeAmount: 'changeAmount',
  createdAt: 'createdAt',
  transactionId: 'transactionId'
};

exports.Prisma.PermissionScalarFieldEnum = {
  id: 'id',
  module: 'module',
  canCreate: 'canCreate',
  canRead: 'canRead',
  canUpdate: 'canUpdate',
  canDelete: 'canDelete',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  userId: 'userId'
};

exports.Prisma.UserOutletAccessScalarFieldEnum = {
  id: 'id',
  assignedAt: 'assignedAt',
  userId: 'userId',
  outletId: 'outletId'
};

exports.Prisma.SettingScalarFieldEnum = {
  id: 'id',
  key: 'key',
  value: 'value',
  category: 'category',
  label: 'label',
  type: 'type',
  options: 'options',
  isSystem: 'isSystem',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.SystemLogScalarFieldEnum = {
  id: 'id',
  type: 'type',
  message: 'message',
  details: 'details',
  outletId: 'outletId',
  userId: 'userId',
  createdAt: 'createdAt'
};

exports.Prisma.TransactionItemScalarFieldEnum = {
  id: 'id',
  quantity: 'quantity',
  price: 'price',
  transactionId: 'transactionId',
  serviceId: 'serviceId',
  name: 'name',
  createdAt: 'createdAt'
};

exports.Prisma.ComboServiceDetailScalarFieldEnum = {
  id: 'id',
  quantity: 'quantity',
  comboServiceId: 'comboServiceId',
  includedServiceId: 'includedServiceId',
  createdAt: 'createdAt'
};

exports.Prisma.ServiceOutletScalarFieldEnum = {
  id: 'id',
  serviceId: 'serviceId',
  outletId: 'outletId',
  createdAt: 'createdAt'
};

exports.Prisma.TherapistServiceCommissionScalarFieldEnum = {
  id: 'id',
  therapistId: 'therapistId',
  serviceId: 'serviceId',
  commission: 'commission',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.ActiveTherapistSessionScalarFieldEnum = {
  id: 'id',
  therapistId: 'therapistId',
  serviceId: 'serviceId',
  startTime: 'startTime',
  endTime: 'endTime',
  transactionId: 'transactionId',
  createdAt: 'createdAt'
};

exports.Prisma.BookingCustomerScalarFieldEnum = {
  id: 'id',
  bookingId: 'bookingId',
  customerId: 'customerId',
  createdAt: 'createdAt'
};

exports.Prisma.TherapistAttendanceScalarFieldEnum = {
  id: 'id',
  therapistId: 'therapistId',
  outletId: 'outletId',
  attendanceType: 'attendanceType',
  attendanceTime: 'attendanceTime',
  createdAt: 'createdAt',
  notes: 'notes'
};

exports.Prisma.UserAttendanceScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  attendanceType: 'attendanceType',
  attendanceTime: 'attendanceTime',
  createdAt: 'createdAt',
  notes: 'notes'
};

exports.Prisma.InventoryCategoryScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  isActive: 'isActive',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.InventoryItemScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  categoryId: 'categoryId',
  outletId: 'outletId',
  totalQuantity: 'totalQuantity',
  goodCondition: 'goodCondition',
  damagedCondition: 'damagedCondition',
  lostCondition: 'lostCondition',
  remainingQuantity: 'remainingQuantity',
  minStockLevel: 'minStockLevel',
  maxStockLevel: 'maxStockLevel',
  unitPrice: 'unitPrice',
  supplier: 'supplier',
  purchaseDate: 'purchaseDate',
  warrantyExpiry: 'warrantyExpiry',
  notes: 'notes',
  isActive: 'isActive',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  createdById: 'createdById'
};

exports.Prisma.InventoryMovementScalarFieldEnum = {
  id: 'id',
  itemId: 'itemId',
  movementType: 'movementType',
  quantity: 'quantity',
  fromStatus: 'fromStatus',
  toStatus: 'toStatus',
  reason: 'reason',
  notes: 'notes',
  createdAt: 'createdAt',
  createdById: 'createdById'
};

exports.Prisma.InventoryAuditScalarFieldEnum = {
  id: 'id',
  title: 'title',
  description: 'description',
  outletId: 'outletId',
  status: 'status',
  scheduledDate: 'scheduledDate',
  startedAt: 'startedAt',
  completedAt: 'completedAt',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  createdById: 'createdById',
  assignedToId: 'assignedToId'
};

exports.Prisma.InventoryAuditItemScalarFieldEnum = {
  id: 'id',
  auditId: 'auditId',
  itemId: 'itemId',
  expectedGood: 'expectedGood',
  expectedDamaged: 'expectedDamaged',
  expectedLost: 'expectedLost',
  actualGood: 'actualGood',
  actualDamaged: 'actualDamaged',
  actualLost: 'actualLost',
  variance: 'variance',
  notes: 'notes',
  isChecked: 'isChecked',
  checkedAt: 'checkedAt',
  checkedById: 'checkedById'
};

exports.Prisma.SortOrder = {
  asc: 'asc',
  desc: 'desc'
};

exports.Prisma.NullableJsonNullValueInput = {
  DbNull: Prisma.DbNull,
  JsonNull: Prisma.JsonNull
};

exports.Prisma.QueryMode = {
  default: 'default',
  insensitive: 'insensitive'
};

exports.Prisma.NullsOrder = {
  first: 'first',
  last: 'last'
};

exports.Prisma.JsonNullValueFilter = {
  DbNull: Prisma.DbNull,
  JsonNull: Prisma.JsonNull,
  AnyNull: Prisma.AnyNull
};
exports.Role = exports.$Enums.Role = {
  ADMIN: 'ADMIN',
  MANAGER: 'MANAGER',
  STAFF: 'STAFF',
  INVESTOR: 'INVESTOR',
  CUSTOMER: 'CUSTOMER',
  ASSISTANT: 'ASSISTANT'
};

exports.Gender = exports.$Enums.Gender = {
  MALE: 'MALE',
  FEMALE: 'FEMALE',
  OTHER: 'OTHER'
};

exports.BookingStatus = exports.$Enums.BookingStatus = {
  PENDING: 'PENDING',
  CONFIRMED: 'CONFIRMED',
  COMPLETED: 'COMPLETED',
  CANCELLED: 'CANCELLED',
  NO_SHOW: 'NO_SHOW'
};

exports.PaymentMethod = exports.$Enums.PaymentMethod = {
  CASH: 'CASH',
  CREDIT_CARD: 'CREDIT_CARD',
  DEBIT_CARD: 'DEBIT_CARD',
  TRANSFER: 'TRANSFER',
  DIGITAL_WALLET: 'DIGITAL_WALLET',
  QRIS: 'QRIS',
  SPLIT: 'SPLIT'
};

exports.PaymentStatus = exports.$Enums.PaymentStatus = {
  PENDING: 'PENDING',
  PAID: 'PAID',
  REFUNDED: 'REFUNDED',
  FAILED: 'FAILED'
};

exports.SettingType = exports.$Enums.SettingType = {
  TEXT: 'TEXT',
  NUMBER: 'NUMBER',
  BOOLEAN: 'BOOLEAN',
  SELECT: 'SELECT',
  COLOR: 'COLOR',
  DATE: 'DATE',
  TIME: 'TIME',
  DATETIME: 'DATETIME',
  IMAGE: 'IMAGE',
  RICHTEXT: 'RICHTEXT'
};

exports.InventoryStatus = exports.$Enums.InventoryStatus = {
  BAIK: 'BAIK',
  RUSAK: 'RUSAK',
  HILANG: 'HILANG'
};

exports.InventoryAuditStatus = exports.$Enums.InventoryAuditStatus = {
  PENDING: 'PENDING',
  IN_PROGRESS: 'IN_PROGRESS',
  COMPLETED: 'COMPLETED',
  CANCELLED: 'CANCELLED'
};

exports.Prisma.ModelName = {
  User: 'User',
  Outlet: 'Outlet',
  Therapist: 'Therapist',
  Service: 'Service',
  Customer: 'Customer',
  Booking: 'Booking',
  BookingService: 'BookingService',
  Transaction: 'Transaction',
  SplitPayment: 'SplitPayment',
  Permission: 'Permission',
  UserOutletAccess: 'UserOutletAccess',
  Setting: 'Setting',
  SystemLog: 'SystemLog',
  TransactionItem: 'TransactionItem',
  ComboServiceDetail: 'ComboServiceDetail',
  ServiceOutlet: 'ServiceOutlet',
  TherapistServiceCommission: 'TherapistServiceCommission',
  ActiveTherapistSession: 'ActiveTherapistSession',
  BookingCustomer: 'BookingCustomer',
  TherapistAttendance: 'TherapistAttendance',
  UserAttendance: 'UserAttendance',
  InventoryCategory: 'InventoryCategory',
  InventoryItem: 'InventoryItem',
  InventoryMovement: 'InventoryMovement',
  InventoryAudit: 'InventoryAudit',
  InventoryAuditItem: 'InventoryAuditItem'
};

/**
 * This is a stub Prisma Client that will error at runtime if called.
 */
class PrismaClient {
  constructor() {
    return new Proxy(this, {
      get(target, prop) {
        let message
        const runtime = getRuntime()
        if (runtime.isEdge) {
          message = `PrismaClient is not configured to run in ${runtime.prettyName}. In order to run Prisma Client on edge runtime, either:
- Use Prisma Accelerate: https://pris.ly/d/accelerate
- Use Driver Adapters: https://pris.ly/d/driver-adapters
`;
        } else {
          message = 'PrismaClient is unable to run in this browser environment, or has been bundled for the browser (running in `' + runtime.prettyName + '`).'
        }

        message += `
If this is unexpected, please open an issue: https://pris.ly/prisma-prisma-bug-report`

        throw new Error(message)
      }
    })
  }
}

exports.PrismaClient = PrismaClient

Object.assign(exports, Prisma)
