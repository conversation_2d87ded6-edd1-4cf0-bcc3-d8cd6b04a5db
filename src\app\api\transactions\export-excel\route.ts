import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import ExcelJS from 'exceljs';
import { format } from 'date-fns';
import { id as idLocale } from 'date-fns/locale';

export async function GET(req: NextRequest) {
  try {

    // Ambil query parameter
    const { searchParams } = new URL(req.url);
    const outletId = searchParams.get('outletId');
    const startDate = searchParams.get('startDate');
    const endDate = searchParams.get('endDate');
    const therapistId = searchParams.get('therapistId');

    console.log(`Export Excel - Parameters: outletId=${outletId}, startDate=${startDate}, endDate=${endDate}, therapistId=${therapistId}`);

    // Buat filter untuk query
    const where: any = {};

    // Filter berdasarkan outlet
    if (outletId) {
      where.outletId = outletId;
    }

    // Filter berdasarkan terapis
    if (therapistId) {
      where.therapistId = therapistId;
    }

    // Filter berdasarkan tanggal
    if (startDate || endDate) {
      where.createdAt = {};

      if (startDate) {
        const startDateTime = new Date(startDate);
        startDateTime.setHours(0, 0, 0, 0);
        where.createdAt.gte = startDateTime;
      }

      if (endDate) {
        const endDateTime = new Date(endDate);
        endDateTime.setHours(23, 59, 59, 999);
        where.createdAt.lte = endDateTime;
      }
    }

    // Ambil data transaksi
    const transactions = await prisma.transaction.findMany({
      where,
      include: {
        customer: {
          select: {
            id: true,
            name: true,
            phone: true,
            tags: true
          }
        },
        therapist: {
          select: {
            id: true,
            name: true
          }
        },
        outlet: {
          select: {
            id: true,
            name: true
          }
        },
        transactionItems: {
          include: {
            service: true
          }
        },
        createdBy: {
          select: {
            id: true,
            name: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    });

    console.log(`Found ${transactions.length} transactions for export`);

    // Buat workbook Excel
    const workbook = new ExcelJS.Workbook();
    workbook.creator = 'Breaktime Dashboard';
    workbook.created = new Date();

    // Buat worksheet
    const worksheet = workbook.addWorksheet('Riwayat Transaksi');

    // Definisikan header
    worksheet.columns = [
      { header: 'Tanggal', key: 'date', width: 20 },
      { header: 'ID Transaksi', key: 'id', width: 15 },
      { header: 'Pelanggan', key: 'customer', width: 20 },
      { header: 'No. HP', key: 'phone', width: 15 },
      { header: 'Tag Pelanggan', key: 'customerTags', width: 20 },
      { header: 'Terapis', key: 'therapist', width: 20 },
      { header: 'Outlet', key: 'outlet', width: 20 },
      { header: 'Layanan', key: 'services', width: 30 },
      { header: 'Metode Pembayaran', key: 'paymentMethod', width: 20 },
      { header: 'Total', key: 'total', width: 15 },
      { header: 'Dibuat Oleh', key: 'createdBy', width: 20 }
    ];

    // Style header
    worksheet.getRow(1).font = { bold: true };
    worksheet.getRow(1).fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: 'FFE0F2F1' } // Warna teal muda
    };

    // Tambahkan data
    transactions.forEach(transaction => {
      // Format tanggal
      const formattedDate = format(
        new Date(transaction.createdAt),
        'dd MMMM yyyy HH:mm',
        { locale: idLocale }
      );

      // Format ID transaksi
      const transactionId = transaction.displayId || `TR${String(transaction.id).padStart(7, '0')}`;

      // Format layanan
      const services = transaction.transactionItems.map(item =>
        `${item.service?.name || 'Layanan'} (${item.quantity}x)`
      ).join(', ');

      // Format tag pelanggan
      const customerTags = transaction.customer?.tags ?
        (Array.isArray(transaction.customer.tags) ?
          transaction.customer.tags.join(', ') :
          transaction.customer.tags.toString()) :
        '-';

      // Tambahkan row
      worksheet.addRow({
        date: formattedDate,
        id: transactionId,
        customer: transaction.customer?.name || 'Pelanggan',
        phone: transaction.customer?.phone || '-',
        customerTags: customerTags,
        therapist: transaction.therapist?.name || '-',
        outlet: transaction.outlet?.name || '-',
        services: services,
        paymentMethod: transaction.paymentMethod,
        total: transaction.totalAmount,
        createdBy: transaction.createdBy?.name || '-'
      });
    });

    // Format kolom total sebagai currency
    worksheet.getColumn('total').numFmt = '"Rp"#,##0';

    // Auto filter
    worksheet.autoFilter = {
      from: { row: 1, column: 1 },
      to: { row: 1, column: 11 }
    };

    // Generate buffer
    const buffer = await workbook.xlsx.writeBuffer();

    // Buat nama file
    const dateStr = format(new Date(), 'yyyyMMdd_HHmmss');
    const fileName = `Riwayat_Transaksi_${dateStr}.xlsx`;

    // Return response
    return new NextResponse(buffer, {
      headers: {
        'Content-Type': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'Content-Disposition': `attachment; filename="${fileName}"`
      }
    });
  } catch (error) {
    console.error('Error exporting transactions to Excel:', error);
    return NextResponse.json(
      { error: 'Terjadi kesalahan saat mengekspor data transaksi' },
      { status: 500 }
    );
  }
}
