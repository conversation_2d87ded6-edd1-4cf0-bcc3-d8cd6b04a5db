import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import {
  calculateTherapistCommission,
  ServiceCommissionData,
  TherapistSpecialCommission,
  validateCommissionConsistency
} from '@/lib/commission-utils';

export async function GET(request: Request) {
  try {
    // Ambil parameter dari query string
    const { searchParams } = new URL(request.url);
    const name = searchParams.get('name');
    const startDate = searchParams.get('startDate');
    const endDate = searchParams.get('endDate');

    // Validasi parameter
    if (!name) {
      return NextResponse.json(
        { message: 'Nama terapis harus diisi' },
        { status: 400 }
      );
    }

    // Konversi string tanggal ke objek Date
    let startDateTime = startDate ? new Date(startDate) : new Date();
    let endDateTime = endDate ? new Date(endDate) : new Date();

    // Set waktu untuk tanggal mulai ke 00:00:00
    startDateTime.setHours(0, 0, 0, 0);

    // Set waktu untuk tanggal akhir ke 23:59:59
    endDateTime.setHours(23, 59, 59, 999);

    // Cari terapis berdasarkan nama
    const therapist = await prisma.therapist.findFirst({
      where: {
        name: {
          contains: name,
          mode: 'insensitive', // Case-insensitive search
        },
        // Hapus filter isActive untuk memastikan semua terapis dapat ditemukan
        // isActive: true,
      },
      include: {
        serviceCommissions: {
          include: {
            service: true
          }
        }
      }
    });

    if (!therapist) {
      // Coba cari dengan nama yang lebih fleksibel (misalnya, hanya sebagian dari nama)
      const partialNameTherapist = await prisma.therapist.findFirst({
        where: {
          OR: [
            { name: { startsWith: name, mode: 'insensitive' } },
            { name: { endsWith: name, mode: 'insensitive' } },
            { name: { contains: name.split(' ')[0], mode: 'insensitive' } }, // Cari berdasarkan kata pertama
          ],
        },
        include: {
          serviceCommissions: {
            include: {
              service: true
            }
          }
        }
      });

      if (partialNameTherapist) {
        return NextResponse.json({
          message: `Terapis dengan nama persis "${name}" tidak ditemukan, tetapi kami menemukan "${partialNameTherapist.name}". Apakah ini yang Anda maksud?`,
          therapistName: partialNameTherapist.name,
          commissions: [],
          totalSales: 0,
          outlets: [],
          suggestedTherapist: partialNameTherapist.name
        });
      }
    }

    // Jika terapis tidak ditemukan
    if (!therapist) {
        // Tidak ada terapis yang ditemukan
      const allTherapists = await prisma.therapist.findMany({
        select: { id: true, name: true, isActive: true }
      });

      return NextResponse.json(
        {
          message: 'Terapis tidak ditemukan',
          allTherapists: allTherapists // Tambahkan daftar semua terapis untuk debugging
        },
        { status: 404 }
      );
    }

    // Ambil data transaksi langsung dari tabel Transaction
    const transactions = await prisma.transaction.findMany({
      where: {
        therapistId: therapist.id,
        transactionDate: {
          gte: startDateTime,
          lte: endDateTime,
        },
        paymentStatus: 'PAID', // Hanya transaksi yang sudah dibayar
      },
      select: {
        id: true,
        displayId: true,
        transactionDate: true,
        therapistCommissionEarned: true,
        totalAmount: true,
        customer: {
          select: {
            name: true,
          },
        },
        outlet: {
          select: {
            name: true,
          },
        },
        transactionItems: {
          select: {
            id: true,
            price: true,
            quantity: true,
            name: true, // Tambahkan field name untuk item kustom
            service: {
              select: {
                id: true,
                name: true,
                commission: true,
                price: true,
              },
            },
          },
        },
      },
      orderBy: {
        transactionDate: 'desc',
      },
    });

    // Definisikan interface untuk service item
    interface ServiceItem {
      name: string;
      price: number;
      commission: number;
      quantity: number; // Tambahkan quantity
    }

    // Kita akan menggunakan tipe 'any' untuk transaction item

    // Definisikan interface untuk transaction item (tidak perlu interface Transaction lengkap)
    // Kita akan menggunakan tipe 'any' untuk transaction

    // Definisikan interface untuk transaction group
    interface TransactionGroup {
      transactionId: string;
      transactionDate: Date;
      customerName: string;
      outletName: string;
      services: ServiceItem[];
      totalAmount: number;
      calculatedCommission: number; // Tambahkan field untuk komisi berdasarkan perhitungan
      therapistCommissionEarned: number; // Simpan nilai dari database
      discountType?: 'percentage' | 'fixed' | 'none'; // Tipe diskon
      discountValue?: number; // Nilai diskon (persentase atau nominal)
      discountAmount?: number; // Jumlah diskon yang diterapkan
      additionalCharge?: number; // Biaya tambahan
    }

    // Kelompokkan transaksi berdasarkan ID transaksi
    const transactionGroups = new Map<string, TransactionGroup>();

    transactions.forEach((transaction: any) => {

      const transactionId = String(transaction.id);
      const transactionDate = transaction.transactionDate;
      const customerName = transaction.customer?.name || 'Pelanggan Umum';
      const outletName = transaction.outlet.name;

      // Gunakan therapistCommissionEarned dari transaksi sebagai nilai komisi utama
      // Nilai ini sudah dihitung berdasarkan harga asli item, bukan harga setelah diskon
      // PERBAIKAN: Jangan ubah null menjadi 0, biarkan null untuk logic prioritas yang benar
      const therapistCommissionEarned = transaction.therapistCommissionEarned;

      // Ambil nilai diskon dan biaya tambahan dari transaksi jika ada
      // Karena field ini mungkin tidak ada di model Transaction lama, kita perlu menggunakan type assertion
      const discountType = (transaction as any).discountType || 'none';
      const discountValue = (transaction as any).discountValue || 0;
      const discountAmount = (transaction as any).discountAmount || 0;
      const additionalCharge = (transaction as any).additionalCharge || 0;

      // Inisialisasi grup transaksi baru
      if (!transactionGroups.has(transactionId)) {
        transactionGroups.set(transactionId, {
          transactionId,
          transactionDate,
          customerName,
          outletName,
          services: [],
          totalAmount: transaction.totalAmount,
          calculatedCommission: 0, // Inisialisasi dengan 0
          therapistCommissionEarned: therapistCommissionEarned,
          discountType,
          discountValue,
          discountAmount,
          additionalCharge
        });
      }

      // Tambahkan layanan ke grup transaksi
      const group = transactionGroups.get(transactionId)!;

      // Proses setiap item transaksi menggunakan utility function
      if (transaction.transactionItems && transaction.transactionItems.length > 0) {
        // Siapkan data untuk utility function
        const serviceCommissionData: ServiceCommissionData[] = [];
        const therapistSpecialCommissions: TherapistSpecialCommission[] = [];

        // Konversi therapist.serviceCommissions ke format yang dibutuhkan
        therapist.serviceCommissions.forEach(sc => {
          therapistSpecialCommissions.push({
            serviceId: sc.serviceId,
            commission: sc.commission
          });
        });

        transaction.transactionItems.forEach((item: any) => {
          const itemQuantity = item.quantity || 1;

          // Cek apakah item memiliki service (bukan item kustom)
          if (item.service && item.service.id) {
            // Tambahkan ke serviceCommissionData
            serviceCommissionData.push({
              serviceId: item.service.id,
              quantity: itemQuantity,
              defaultCommission: item.service.commission || 0
            });

            // Tentukan komisi yang akan digunakan untuk tampilan
            const specialCommission = therapist.serviceCommissions.find(
              sc => sc.serviceId === item.service.id
            );
            const commissionPerItem = specialCommission
              ? specialCommission.commission
              : item.service.commission;

            // Cek apakah layanan dengan nama yang sama sudah ada
            const existingServiceIndex = group.services.findIndex(s => s.name === item.service.name);

            if (existingServiceIndex !== -1) {
              // Jika layanan sudah ada, tambahkan quantity
              group.services[existingServiceIndex].quantity += itemQuantity;
            } else {
              // Jika layanan belum ada, tambahkan sebagai layanan baru
              group.services.push({
                name: item.service.name,
                price: item.price,
                commission: commissionPerItem, // Komisi per item (tanpa memperhitungkan quantity)
                quantity: itemQuantity
              });
            }
          } else {
            // Item kustom (tidak ada service)
            const itemName = item.name || 'Item Kustom';
            
            // Cek apakah item kustom dengan nama yang sama sudah ada
            const existingServiceIndex = group.services.findIndex(s => s.name === itemName);

            if (existingServiceIndex !== -1) {
              // Jika item sudah ada, tambahkan quantity
              group.services[existingServiceIndex].quantity += itemQuantity;
            } else {
              // Jika item belum ada, tambahkan sebagai item baru dengan komisi 0
              group.services.push({
                name: itemName,
                price: item.price,
                commission: 0, // Item kustom tidak ada komisi
                quantity: itemQuantity
              });
            }
          }
        });

        // Hitung komisi menggunakan utility function
        const calculatedCommission = calculateTherapistCommission(
          serviceCommissionData,
          therapistSpecialCommissions
        );

        // Validasi konsistensi jika ada nilai dari database
        if (transaction.therapistCommissionEarned !== null && transaction.therapistCommissionEarned !== undefined) {
          const isConsistent = validateCommissionConsistency(
            calculatedCommission,
            transaction.therapistCommissionEarned
          );

          if (!isConsistent) {
            console.warn(`Commission inconsistency detected for transaction ${transactionId}:`, {
              calculated: calculatedCommission,
              stored: transaction.therapistCommissionEarned,
              difference: Math.abs(calculatedCommission - transaction.therapistCommissionEarned)
            });
          }
        }

        // Cek apakah transaksi memiliki biaya tambahan
        if (transaction.additionalCharge && transaction.additionalCharge > 0) {
          // Tidak menambahkan biaya tambahan ke komisi terapis
          // Biaya tambahan hanya mempengaruhi pendapatan terapis, bukan komisi
          group.additionalCharge = transaction.additionalCharge;
        }

        // Cek apakah transaksi memiliki diskon
        if (transaction.discountType && transaction.discountType !== 'none' && transaction.discountAmount > 0) {
          // Simpan informasi diskon di grup transaksi (untuk tampilan saja)
          group.discountType = transaction.discountType;
          group.discountValue = transaction.discountValue || 0;
          group.discountAmount = transaction.discountAmount;
          // Diskon tidak mempengaruhi pendapatan terapis, jadi tidak perlu dikurangkan dari komisi
        }

        // Simpan komisi yang dihitung menggunakan utility function
        group.calculatedCommission = calculatedCommission;
      }
    });

    // Konversi Map ke array untuk respons
    const commissions = Array.from(transactionGroups.values()).map(group => {
      // Gabungkan nama layanan untuk tampilan dengan quantity
      const serviceName = group.services.map((s: ServiceItem) =>
        s.quantity > 1 ? `${s.name} (${s.quantity}x)` : s.name
      ).join(', ');

      // Prioritaskan nilai dari database jika tersedia (bahkan jika 0), jika tidak gunakan nilai yang dihitung
      // Ini untuk memastikan kompatibilitas dengan data lama dan konsistensi dengan nilai yang disimpan
      const commissionValue = group.therapistCommissionEarned !== null && group.therapistCommissionEarned !== undefined
        ? group.therapistCommissionEarned
        : group.calculatedCommission;

      return {
        transactionId: group.transactionId,
        transactionDate: group.transactionDate,
        serviceName: serviceName,
        customerName: group.customerName,
        amount: group.totalAmount,
        commission: commissionValue,
        outletName: group.outletName,
        // Tambahkan array services untuk detail
        services: group.services,
        // Tambahkan informasi diskon jika ada
        discountType: group.discountType || 'none',
        discountValue: group.discountValue || 0,
        discountAmount: group.discountAmount || 0,
        // Tambahkan informasi biaya tambahan jika ada
        additionalCharge: group.additionalCharge || 0
      };
    });

    // Hitung total penjualan
    const totalSales = Array.from(transactionGroups.values()).reduce(
      (sum, group) => sum + group.totalAmount,
      0
    );

    // Hitung total komisi (prioritaskan nilai dari database, fallback ke nilai yang dihitung)
    const totalCommission = Array.from(transactionGroups.values()).reduce(
      (sum, group) => {
        const commissionValue = group.therapistCommissionEarned !== null && group.therapistCommissionEarned !== undefined
          ? group.therapistCommissionEarned
          : group.calculatedCommission;
        return sum + commissionValue;
      },
      0
    );

    // Dapatkan daftar outlet unik yang dilayani oleh terapis
    const uniqueOutlets = [...new Set(transactions.map((item: any) => item.outlet.name))];

    // Tambahkan pesan khusus jika tidak ada transaksi
    const message = commissions.length > 0
      ? 'Data komisi berhasil diambil'
      : `Tidak ada data komisi untuk terapis ${therapist.name} dalam rentang tanggal yang dipilih`;

    return NextResponse.json({
      message,
      therapistName: therapist.name,
      commissions,
      totalSales,
      totalCommission, // Tambahkan total komisi yang sudah dihitung dengan benar
      outlets: uniqueOutlets,
      hasData: commissions.length > 0
    });
  } catch (error) {
    console.error('[API GET /api/therapists/commissions] Error:', error);

    return NextResponse.json(
      { message: 'Terjadi kesalahan saat mengambil data komisi' },
      { status: 500 }
    );
  }
}
