import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { Prisma } from '@prisma/client';

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const outletId = searchParams.get('outletId');

    // Ambil parameter tanggal dari query
    const rawStartDate = searchParams.get('startDate');
    const rawEndDate = searchParams.get('endDate');

    if (!rawStartDate || !rawEndDate) {
      throw new Error('Tanggal mulai dan tanggal akhir harus disediakan');
    }

    // Parse tanggal
    const startDate = new Date(rawStartDate);
    const endDate = new Date(rawEndDate);

    // Validasi tanggal
    if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
      throw new Error('Format tanggal tidak valid');
    }

    // Filter dasar untuk transaksi berdasarkan tanggal dan outlet terpilih
    const transactionWhere: Prisma.TransactionWhereInput = {
      transactionDate: { gte: startDate, lte: endDate },
      ...(outletId && { outletId: outletId }),
    };

    // 1. Ambil semua layanan
    const services = await prisma.service.findMany({
      select: {
        id: true,
        name: true,
        price: true,
        commission: true,
      },
    });

    // 2. Ambil data transaksi item untuk menghitung jumlah penggunaan layanan
    const transactionItems = await prisma.transactionItem.findMany({
      where: {
        transaction: transactionWhere,
      },
      select: {
        serviceId: true,
        quantity: true,
        price: true,
        transaction: {
          select: {
            transactionDate: true,
          },
        },
      },
    });

    // 3. Hitung performa layanan
    const servicePerformance = services.map(service => {
      // Filter transaction items untuk layanan ini
      const serviceItems = transactionItems.filter(item => item.serviceId === service.id);
      
      // Hitung jumlah penggunaan
      const usageCount = serviceItems.reduce((sum, item) => sum + (item.quantity || 1), 0);
      
      // Hitung total pendapatan
      const totalRevenue = serviceItems.reduce((sum, item) => sum + (item.price * (item.quantity || 1)), 0);
      
      // Hitung komisi total
      const totalCommission = usageCount * (service.commission || 0);
      
      // Hitung keuntungan (profit)
      const profit = totalRevenue - totalCommission;
      
      // Hitung margin keuntungan
      const margin = totalRevenue > 0 ? (profit / totalRevenue) * 100 : 0;

      // Hitung tren penggunaan (sederhana: bandingkan paruh pertama dan kedua periode)
      const sortedItems = [...serviceItems].sort((a, b) => 
        new Date(a.transaction.transactionDate).getTime() - new Date(b.transaction.transactionDate).getTime()
      );
      
      const midPoint = Math.floor(sortedItems.length / 2);
      const firstHalfCount = sortedItems.slice(0, midPoint).reduce((sum, item) => sum + (item.quantity || 1), 0);
      const secondHalfCount = sortedItems.slice(midPoint).reduce((sum, item) => sum + (item.quantity || 1), 0);
      
      const trend = firstHalfCount > 0 
        ? ((secondHalfCount - firstHalfCount) / firstHalfCount) * 100 
        : (secondHalfCount > 0 ? 100 : 0);

      // Rekomendasi harga berdasarkan margin dan tren
      let priceRecommendation = service.price;
      let recommendationReason = '';
      
      if (usageCount === 0) {
        recommendationReason = 'Tidak ada data penggunaan yang cukup untuk rekomendasi.';
      } else if (margin < 20 && trend >= 0) {
        // Jika margin rendah tapi tren positif/stabil, naikkan harga sedikit
        priceRecommendation = service.price * 1.05;
        recommendationReason = 'Margin rendah dengan permintaan stabil/meningkat. Pertimbangkan kenaikan harga moderat.';
      } else if (margin < 20 && trend < 0) {
        // Jika margin rendah dan tren negatif, pertahankan harga
        recommendationReason = 'Margin rendah dengan permintaan menurun. Pertahankan harga dan tingkatkan promosi.';
      } else if (margin >= 20 && margin < 40 && trend < -10) {
        // Jika margin sedang tapi tren sangat negatif, turunkan harga sedikit
        priceRecommendation = service.price * 0.95;
        recommendationReason = 'Permintaan menurun signifikan. Pertimbangkan penurunan harga kecil untuk meningkatkan volume.';
      } else if (margin >= 40 && trend < 0) {
        // Jika margin tinggi tapi tren negatif, turunkan harga
        priceRecommendation = service.price * 0.9;
        recommendationReason = 'Margin tinggi dengan permintaan menurun. Pertimbangkan penurunan harga untuk meningkatkan volume.';
      } else if (margin >= 40 && trend >= 10) {
        // Jika margin tinggi dan tren sangat positif, naikkan harga
        priceRecommendation = service.price * 1.1;
        recommendationReason = 'Permintaan tinggi dengan margin baik. Pertimbangkan kenaikan harga untuk memaksimalkan profit.';
      } else {
        recommendationReason = 'Harga saat ini sudah optimal berdasarkan margin dan permintaan.';
      }

      return {
        serviceId: service.id,
        serviceName: service.name,
        currentPrice: service.price,
        commission: service.commission || 0,
        usageCount,
        totalRevenue,
        profit,
        margin,
        trend,
        priceRecommendation,
        recommendationReason,
      };
    }).filter(service => service.usageCount > 0) // Filter hanya layanan yang digunakan
      .sort((a, b) => b.totalRevenue - a.totalRevenue); // Urutkan berdasarkan pendapatan

    return NextResponse.json({
      message: 'Data performa layanan berhasil diambil',
      servicePerformance,
      filters: {
        startDate: startDate.toISOString(),
        endDate: endDate.toISOString(),
        outletId: outletId,
      },
    });
  } catch (error: unknown) {
    console.error('[API GET /api/reports/service-performance] Error:', error);

    let errorMessage = 'Terjadi kesalahan saat mengambil data performa layanan';
    const statusCode = 500;

    if (error instanceof Error) {
      errorMessage = error.message;
    }

    return NextResponse.json({
      error: errorMessage,
      message: 'Terjadi kesalahan saat mengambil data performa layanan',
      servicePerformance: [],
      filters: {
        startDate: new Date().toISOString(),
        endDate: new Date().toISOString(),
        outletId: null,
      },
    }, { status: statusCode });
  }
}
